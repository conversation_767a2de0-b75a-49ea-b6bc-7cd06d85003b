import { LogLevel } from "@eait-playerexp-cn/activity-logger";
import { Environment, RedisClientOptions, ScaleReadType, SessionOptions } from "@eait-playerexp-cn/server-kernel";

const env = new Environment(process.env);

const redisClient = new RedisClientOptions(
  env.get("REDIS_HOST"),
  env.parseNumber("REDIS_PORT"),
  env.get("REDIS_SCALE_READ") as ScaleReadType
);

const config = {
  APP_ENV: env.get("APP_ENV"),
  SENTRY_DSN: env.get("SENTRY_DSN"),
  SUPPORTED_LOCALES: env.parseArray<string>("SUPPORTED_LOCALES"),
  HTTP_REQUEST_TIMEOUT: env.parseNumber("HTTP_REQUEST_TIMEOUT"),
  LOG_LEVEL: env.get("LOG_LEVEL") as LogLevel,
  SESSION_TTL: env.parseNumber("SESSION_TTL"),
  API_CLIENT_ID: env.get("API_CLIENT_ID"),
  API_CLIENT_SECRET: env.get("API_CLIENT_SECRET"),
  METADATA_API_BASE_URL: env.get("METADATA_API_BASE_URL"),
  CONTENT_SCANNING_API_BASE_URL: env.get("CONTENT_SCANNING_API_BASE_URL"),
  CONTENT_SUBMISSION_BASE_URL: env.get("CONTENT_SUBMISSION_BASE_URL"),
  COMMUNICATIONS_API_BASE_URL: env.get("COMMUNICATIONS_API_BASE_URL"),
  ACCESS_TOKEN_BASE_URL: env.get("ACCESS_TOKEN_BASE_URL"),
  OPERATIONS_API_BASE_URL: env.get("OPERATIONS_API_BASE_URL"),
  LEGAL_API_BASE_URL: env.get("LEGAL_API_BASE_URL"),
  APP_DEBUG: env.parseBoolean("APP_DEBUG"),
  TERMS_STATUS_CACHE_TTL: env.parseNumber("TERMS_STATUS_CACHE_TTL"),
  INTERESTED_CREATOR_REAPPLY_PERIOD: env.parseBoolean("INTERESTED_CREATOR_REAPPLY_PERIOD"),
  redisClient,
  sessionOptions: {
    cookies: {
      secret: env.get("COOKIE_PASSWORD"),
      httpOnly: env.parseBoolean("COOKIE_HTTP_ONLY"),
      sameSite: env.get("COOKIE_SAME_SITE"),
      domain: env.get("COOKIE_DOMAIN", undefined),
      secure: env.parseBoolean("COOKIE_SECURE")
    },
    ttl: env.parseNumber("SESSION_TTL"),
    proxy: env.parseBoolean("SESSION_PROXY")
  } as SessionOptions,
  cacheOptions: {
    cachePrefix: env.get("CACHE_PREFIX"),
    redisClient
  },
  corsConfiguration: {
    allowCredentials: env.get("CORS_ALLOW_CREDENTIALS"),
    allowedMethods: env.get("CORS_ALLOWED_METHODS"),
    allowedOrigins: env.parseCsv("CORS_ALLOWED_ORIGINS"),
    allowedHeaders: env.get("CORS_ALLOWED_HEADERS")
  },
  PROGRAM_CODE: env.get("PROGRAM_CODE"),
  DEVICE_TYPE: env.get("DEVICE_TYPE"),
  PLATFORM: env.get("PLATFORM")
};

export default config;
