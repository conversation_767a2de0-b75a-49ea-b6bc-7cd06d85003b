import "reflect-metadata";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { HttpStatus } from "@eait-playerexp-cn/http";
import ContentScanningHttpClient from "@src/server/creatorCode/ContentScanningHttpClient";

describe("ContentScanningHttpClient", () => {
  beforeEach(() => jest.clearAllMocks());

  it("throws error if it's an unauthorized user", async () => {
    const existingApplication = {
      error: "unauthorized",
      error_description: "An Authentication object was not found in the SecurityContext"
    };
    const mockPost = jest.fn().mockReturnValue(existingApplication);
    const client = { post: mockPost };
    const contentURLs = { urls: ["https://reddit.com", "https://google.com"] };
    const applications = new ContentScanningHttpClient(client as unknown as TraceableHttpClient);

    const application = await applications.verifyUrls(contentURLs, "INTERESTED_CREATORS");

    expect(application).toEqual(existingApplication);
    expect(mockPost).toHaveBeenCalledTimes(1);
    expect(mockPost).toHaveBeenCalledWith("/v1/secure-content", {
      body: { urls: contentURLs },
      query: { type: "INTERESTED_CREATORS" }
    });
  });

  it("throws error if it's an invalid input", async () => {
    const existingApplication = {
      code: "validate-content-urls-invalid-input",
      message: "Cannot validate content urls, invalid input provided",
      title: "Unprocessable Entity",
      type: "https://tools.ietf.org//html//rfc4918#section-11.2",
      status: HttpStatus.UNPROCESSABLE_ENTITY_CODE,
      errors: {
        "urls[1]": "URL is invalid"
      }
    };
    const mockPost = jest.fn().mockReturnValue(existingApplication);
    const client = { post: mockPost };
    const contentURLs = { urls: ["http://reddit.com", "http://google.com"] };
    const applications = new ContentScanningHttpClient(client as unknown as TraceableHttpClient);

    const application = await applications.verifyUrls(contentURLs, "INTERESTED_CREATORS");

    expect(application).toEqual(existingApplication);
    expect(mockPost).toHaveBeenCalledTimes(1);
    expect(mockPost).toHaveBeenCalledWith("/v1/secure-content", {
      body: { urls: contentURLs },
      query: { type: "INTERESTED_CREATORS" }
    });
  });

  it("throws error if HTTP request fails", async () => {
    const existingApplication = {
      status: HttpStatus.INTERNAL_SERVER_ERROR_CODE,
      code: "view-aggregate-error",
      message: "Cannot view aggregate because: {exception.message}",
      exception: {
        message: "Illegal base64 character 25",
        class: "com.eaitplayerexp.somepackage.SomeClass",
        file: "SomeClass.class",
        line: 298,
        stackTrace: [],
        previous: "string"
      },
      title: "Internal Server Error",
      type: "https://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html#sec10.5.1"
    };
    const mockPost = jest.fn().mockReturnValue(existingApplication);
    const client = { post: mockPost };
    const contentURLs = { urls: ["http://reddit.com", "http://google.com"] };
    const applications = new ContentScanningHttpClient(client as unknown as TraceableHttpClient);

    const application = await applications.verifyUrls(contentURLs, "INTERESTED_CREATORS");

    expect(application).toEqual(existingApplication);
    expect(mockPost).toHaveBeenCalledTimes(1);
    expect(mockPost).toHaveBeenCalledWith("/v1/secure-content", {
      body: { urls: contentURLs },
      query: { type: "INTERESTED_CREATORS" }
    });
  });

  it("urls verified successfully", async () => {
    const existingApplication = {
      results: [
        {
          url: "https://www.reddit.com",
          isSecure: true
        }
      ]
    };
    const mockPost = jest.fn().mockReturnValue(existingApplication);
    const client = { post: mockPost };
    const contentURLs = { urls: ["https://reddit.com"] };
    const applications = new ContentScanningHttpClient(client as unknown as TraceableHttpClient);

    const application = await applications.verifyUrls(contentURLs, "INTERESTED_CREATORS");

    expect(application).toEqual(existingApplication);
    expect(mockPost).toHaveBeenCalledTimes(1);
    expect(mockPost).toHaveBeenCalledWith("/v1/secure-content", {
      body: { urls: contentURLs },
      query: { type: "INTERESTED_CREATORS" }
    });
  });
});
