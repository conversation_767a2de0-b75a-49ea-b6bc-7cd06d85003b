{"files": {"src/middleware/OAuthErrorHandler.ts": {"language": "typescript", "mutants": [{"id": "0", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: {\"code\": \"view-terms-and-conditions-url-invalid-input\", \"message\": \"Cannot view terms and conditions url. Invalid input provided\"}\nReceived: undefined\n    at toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:49:33)", "status": "Killed", "testsCompleted": 4, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1", "2", "3"], "location": {"end": {"column": 2, "line": 28}, "start": {"column": 18, "line": 13}}}, {"id": "1", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "TypeError: Cannot read properties of undefined (reading 'data')\n    at data (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/src/middleware/OAuthErrorHandler.ts:71:56)\n    at Object.<anonymous> (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:62:23)\n    at Promise.then.completed (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:316:40)\n    at _runTest (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:121:9)\n    at run (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-runner/build/runTest.js:444:34)", "status": "Killed", "testsCompleted": 4, "static": false, "killedBy": ["2"], "coveredBy": ["0", "1", "2", "3"], "location": {"end": {"column": 43, "line": 17}, "start": {"column": 7, "line": 17}}}, {"id": "2", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 0\nReceived number of calls: 1\n    at toHaveBeenCalledTimes (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:48:24)", "status": "Killed", "testsCompleted": 4, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1", "2", "3"], "location": {"end": {"column": 43, "line": 17}, "start": {"column": 7, "line": 17}}}, {"id": "3", "mutatorName": "OptionalChaining", "replacement": "(error as AxiosError).response.data", "statusReason": "TypeError: Cannot read properties of undefined (reading 'data')\n    at data (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/src/middleware/OAuthErrorHandler.ts:62:66)\n    at Object.<anonymous> (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:62:23)\n    at Promise.then.completed (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/utils.js:231:10)\n    at _callCircusTest (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:316:40)\n    at _runTest (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:252:3)\n    at _runTestsForDescribeBlock (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:126:9)\n    at _runTestsForDescribeBlock (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:121:9)\n    at run (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/run.js:71:3)\n    at runAndTransformResultsToJestFormat (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:122:21)\n    at jestAdapter (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:79:19)\n    at runTestInternal (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-runner/build/runTest.js:367:16)\n    at runTest (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/node_modules/jest-runner/build/runTest.js:444:34)", "status": "Killed", "testsCompleted": 4, "static": false, "killedBy": ["2"], "coveredBy": ["0", "1", "2", "3"], "location": {"end": {"column": 43, "line": 17}, "start": {"column": 7, "line": 17}}}, {"id": "4", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: {\"code\": \"view-terms-and-conditions-url-invalid-input\", \"message\": \"Cannot view terms and conditions url. Invalid input provided\"}\nReceived: undefined\n    at toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:49:33)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1"], "location": {"end": {"column": 4, "line": 20}, "start": {"column": 45, "line": 17}}}, {"id": "5", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 4\n+ Received  + 1\n\n- Object {\n-   \"code\": \"view-terms-and-conditions-url-invalid-input\",\n-   \"message\": \"Cannot view terms and conditions url. Invalid input provided\",\n- }\n+ Object {}\n    at toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:49:33)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1"], "location": {"end": {"column": 61, "line": 19}, "start": {"column": 25, "line": 19}}}, {"id": "6", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n  Object {\n    \"code\": \"view-terms-and-conditions-url-invalid-input\",\n-   \"message\": \"Cannot view terms and conditions url. Invalid input provided\",\n+   \"message\": true,\n  }\n    at toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:49:33)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1"], "location": {"end": {"column": 59, "line": 19}, "start": {"column": 42, "line": 19}}}, {"id": "7", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n  Object {\n    \"code\": \"view-terms-and-conditions-url-invalid-input\",\n-   \"message\": \"Cannot view terms and conditions url. Invalid input provided\",\n+   \"message\": false,\n  }\n    at toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:49:33)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1"], "location": {"end": {"column": 59, "line": 19}, "start": {"column": 42, "line": 19}}}, {"id": "8", "mutatorName": "LogicalOperator", "replacement": "message && detail", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n  Object {\n    \"code\": \"view-terms-and-conditions-url-invalid-input\",\n-   \"message\": \"Cannot view terms and conditions url. Invalid input provided\",\n+   \"message\": undefined,\n  }\n    at toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:49:33)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1"], "location": {"end": {"column": 59, "line": 19}, "start": {"column": 42, "line": 19}}}, {"id": "9", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:64:22)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["2"], "coveredBy": ["2", "3"], "location": {"end": {"column": 4, "line": 23}, "start": {"column": 10, "line": 20}}}, {"id": "10", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: \"Connection timed out\"\nReceived: true\n    at Object.toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:74:31)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["2"], "coveredBy": ["2", "3"], "location": {"end": {"column": 63, "line": 22}, "start": {"column": 25, "line": 22}}}, {"id": "11", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: \"Connection timed out\"\nReceived: false\n    at Object.toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:74:31)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["2"], "coveredBy": ["2", "3"], "location": {"end": {"column": 63, "line": 22}, "start": {"column": 25, "line": 22}}}, {"id": "12", "mutatorName": "LogicalOperator", "replacement": "error.message && \"No message provided\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: \"Connection timed out\"\nReceived: \"No message provided\"\n    at Object.toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:74:31)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["2"], "coveredBy": ["2", "3"], "location": {"end": {"column": 63, "line": 22}, "start": {"column": 25, "line": 22}}}, {"id": "13", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: \"No message provided\"\nReceived: \"\"\n    at Object.toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:95:31)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["3"], "coveredBy": ["3"], "location": {"end": {"column": 63, "line": 22}, "start": {"column": 42, "line": 22}}}, {"id": "14", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"<script>window.close();</script>\"\nReceived: \"\"\n\nNumber of calls: 1\n    at toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/middleware/OAuthErrorHandler.spec.ts:56:23)", "status": "Killed", "testsCompleted": 4, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1", "2", "3"], "location": {"end": {"column": 45, "line": 27}, "start": {"column": 11, "line": 27}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport ApplicationActivity from \"@src/shared/logging/ApplicationActivity\";\r\nimport { AxiosError } from \"axios\";\r\nimport { ApiProblem } from \"@eait-playerexp-cn/api-problem\";\r\nimport ApiContainer from \"@src/ApiContainer\";\r\nimport { ActivityFeed } from \"@eait-playerexp-cn/activity-feed\";\r\nimport { NextApiRequestWithMultipartFile, RequestFactory } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\nasync function onOAuthError(\r\n  error: Error | AxiosError,\r\n  req: NextApiRequestWithMultipartFile,\r\n  res: NextApiResponse\r\n): Promise<void> {\r\n  const requestFactory = new RequestFactory();\r\n  const feed = ApiContainer.get(ActivityFeed);\r\n\r\n  if ((error as AxiosError).response?.data) {\r\n    const { code, message, detail } = (error as AxiosError<ApiProblem>).response.data;\r\n    req.session.error = { code, message: message || detail }; // Error from Java API\r\n  } else {\r\n    feed.add(ApplicationActivity.applicationError(error, requestFactory.createRequestFrom(req)));\r\n    req.session.error = error.message || \"No message provided\"; // Error message from API handler\r\n  }\r\n\r\n  req.session.save();\r\n\r\n  res.end(\"<script>window.close();</script>\");\r\n}\r\n\r\nexport default onOAuthError;\r\n"}, "src/middleware/Session.ts": {"language": "typescript", "mutants": [{"id": "15", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 65, "line": 5}, "start": {"column": 49, "line": 5}}}], "source": "import \"reflect-metadata\";\r\nimport { sessionFactory } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ApiContainer from \"@src/ApiContainer\";\r\n\r\nconst session = sessionFactory(ApiContainer.get(\"sessionOptions\"));\r\n\r\nexport default session;\r\n"}, "src/middleware/WithCors.ts": {"language": "typescript", "mutants": [{"id": "16", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 70, "line": 4}, "start": {"column": 51, "line": 4}}}], "source": "import { withCorsFactory } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ApiContainer from \"@src/ApiContainer\";\r\n\r\nconst withCors = withCorsFactory(ApiContainer.get(\"corsConfiguration\"));\r\n\r\nexport default withCors;\r\n"}, "src/pages/index.tsx": {"language": "typescript", "mutants": [{"id": "17", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/pages/index.tsx(4,7): error TS2322: Type '() => void' is not assignable to type 'FC'.\n  Type 'void' is not assignable to type 'ReactNode'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["33"], "location": {"end": {"column": 2, "line": 18}, "start": {"column": 24, "line": 4}}}], "source": "import { FC } from \"react\";\r\nimport Head from \"next/head\";\r\n\r\nconst Home: FC = () => {\r\n  return (\r\n    <>\r\n      <Head>\r\n        <title>OnBoarding Federated Service for EA Example</title>\r\n        <meta name=\"description\" content=\"OnBoarding Federated Service for EA Example\" />\r\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n        <link rel=\"icon\" href=\"/favicon.ico\" />\r\n      </Head>\r\n      <main>\r\n        <div className=\"home\">Onboarding Module Federation</div>\r\n      </main>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"}, "src/server/Avatar/AvatarInput.ts": {"language": "typescript", "mutants": [{"id": "18", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/Avatar/AvatarInput.ts(12,20): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["27"], "location": {"end": {"column": 4, "line": 21}, "start": {"column": 33, "line": 12}}}, {"id": "19", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["27"], "location": {"end": {"column": 29, "line": 14}, "start": {"column": 21, "line": 14}}}, {"id": "20", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["27"], "location": {"end": {"column": 6, "line": 18}, "start": {"column": 74, "line": 14}}}], "source": "import AvatarUpload from \"./AvatarUpload\";\r\nimport fs from \"fs\";\r\nimport { FormDataBody } from \"@eait-playerexp-cn/http\";\r\nimport { UploadedFile } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\nexport default class AvatarInput {\r\n  constructor(\r\n    private readonly creatorId: string,\r\n    private readonly avatar: UploadedFile\r\n  ) {}\r\n\r\n  uploadedImage(): AvatarUpload {\r\n    const formData = new FormDataBody();\r\n    formData.append(\"avatar\", fs.createReadStream(this.avatar.filepath), {\r\n      knownLength: this.avatar.size,\r\n      filename: this.avatar.originalFilename,\r\n      contentType: this.avatar.mimetype\r\n    });\r\n\r\n    return new AvatarUpload(this.creatorId, formData);\r\n  }\r\n}\r\n"}, "src/server/Avatar/UploadCreatorAvatarAction.ts": {"language": "typescript", "mutants": [{"id": "21", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (/Users/<USER>/ea/onboarding-micro-frontend/strykerTmp/sandbox5910271/__tests__/src/actions/Creators/Avatar/AvatarAction.spec.ts:28:29)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["27"], "coveredBy": ["27"], "location": {"end": {"column": 4, "line": 14}, "start": {"column": 52, "line": 12}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport AvatarInput from \"./AvatarInput\";\r\nimport CreatorsHttpClient from \"@src/server/creators/CreatorsHttpClient\";\r\n\r\n@Service()\r\nclass UploadCreatorAvatarAction {\r\n  constructor(\r\n    @Inject(\"creators\")\r\n    private readonly creators: CreatorsHttpClient\r\n  ) {}\r\n\r\n  async execute(input: AvatarInput): Promise<void> {\r\n    await this.creators.upload(input.uploadedImage());\r\n  }\r\n}\r\n\r\nexport default UploadCreatorAvatarAction;\r\n"}, "src/server/channels/discord/DiscordAccountHttpClient.ts": {"language": "typescript", "mutants": [{"id": "22", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\strykerTmp\\sandbox1964636\\__tests__\\src\\server\\channels\\discord\\DiscordAccountHttpClient.spec.ts:15:27)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["30"], "coveredBy": ["30"], "location": {"end": {"column": 4, "line": 11}, "start": {"column": 80, "line": 9}}}, {"id": "23", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"/v1/creators/7cc8233e-c914-4396-aa64-511bff681278/discord-accounts/e3edc6c6-ceb0-44cc-8ecb-5387d2274dbe\"\nReceived: \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\strykerTmp\\sandbox1964636\\__tests__\\src\\server\\channels\\discord\\DiscordAccountHttpClient.spec.ts:16:27)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["30"], "coveredBy": ["30"], "location": {"end": {"column": 80, "line": 10}, "start": {"column": 30, "line": 10}}}], "source": "import ConnectedDiscordAccount from \"./ConnectedDiscordAccount\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\n\r\n@Service()\r\nexport default class DiscordAccountHttpClient implements ConnectedDiscordAccount {\r\n  constructor(@Inject(\"communicationsClient\") private client: TraceableHttpClient) {}\r\n\r\n  async disconnectDiscordAccount(creatorId: string, id: string): Promise<void> {\r\n    await this.client.delete(`/v1/creators/${creatorId}/discord-accounts/${id}`);\r\n  }\r\n}\r\n"}, "src/server/channels/facebook/ClearFacebookPagesController.ts": {"language": "typescript", "mutants": [{"id": "24", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/channels/facebook/ClearFacebookPagesController.ts(13,3): error TS2377: Constructors for derived classes must contain a 'super' call.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 15}, "start": {"column": 66, "line": 13}}}, {"id": "25", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 20}, "start": {"column": 85, "line": 17}}}, {"id": "26", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 48, "line": 18}, "start": {"column": 39, "line": 18}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport {\r\n  Controller,\r\n  NextApiRequestWithSession,\r\n  RequestHandler,\r\n  type RequestHandlerOptions\r\n} from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\n\r\n@Service()\r\nclass ClearFacebookPagesController extends RequestHandler implements Controller {\r\n  constructor(@Inject(\"options\") options: RequestHandlerOptions) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    await this.removeFromSession(req, \"fbPages\");\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default ClearFacebookPagesController;\r\n"}, "src/server/channels/facebook/ConnectFacebookPageController.ts": {"language": "typescript", "mutants": [{"id": "27", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 41}, "start": {"column": 85, "line": 18}}}, {"id": "28", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 18, "line": 23}, "start": {"column": 9, "line": 23}}}, {"id": "29", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 18, "line": 23}, "start": {"column": 9, "line": 23}}}, {"id": "30", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 29}, "start": {"column": 20, "line": 23}}}, {"id": "31", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 32}, "start": {"column": 12, "line": 29}}}, {"id": "32", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 48, "line": 34}, "start": {"column": 39, "line": 34}}}, {"id": "33", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 47, "line": 38}, "start": {"column": 34, "line": 38}}}, {"id": "34", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 59, "line": 38}, "start": {"column": 49, "line": 38}}}], "source": "import { AuthenticatedRequestHand<PERSON> } from \"@eait-playerexp-cn/identity\";\r\nimport { NextApiResponse } from \"next\";\r\nimport FacebookPageCredentials from \"./FacebookPageCredentials\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\nimport ConnectedAccountsHttpClient from \"../../connecteAccounts/ConnectedAccountsHttpClient\";\r\n\r\n@Service()\r\nclass ConnectFacebookPageController extends AuthenticatedRequestHandler implements Controller {\r\n  constructor(\r\n    @Inject(\"options\") options: RequestHandlerOptions,\r\n    private readonly connectedAccounts: ConnectedAccountsHttpClient\r\n  ) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const nucleusId = this.session(req, \"nucleusId\") as string;\r\n    const { pageId, pageAccessToken } = req.body;\r\n    let credentials;\r\n\r\n    if (nucleusId) {\r\n      credentials = FacebookPageCredentials.forInterestedCreator(\r\n        pageId as string,\r\n        pageAccessToken as string,\r\n        nucleusId\r\n      );\r\n    } else {\r\n      const identity = this.identity(req);\r\n      credentials = FacebookPageCredentials.forCreator(pageId as string, pageAccessToken as string, identity.id);\r\n    }\r\n\r\n    await this.removeFromSession(req, \"fbPages\");\r\n\r\n    await this.connectedAccounts.connectFacebookPage(credentials);\r\n\r\n    await this.addToSession(req, \"accountType\", \"Facebook\");\r\n\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default ConnectFacebookPageController;\r\n"}, "src/server/channels/facebook/FacebookPageCredentials.ts": {"language": "typescript", "mutants": [{"id": "35", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/channels/facebook/FacebookPageCredentials.ts(2,82): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 4}, "start": {"column": 106, "line": 2}}}, {"id": "36", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/channels/facebook/FacebookPageCredentials.ts(6,92): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 8}, "start": {"column": 116, "line": 6}}}], "source": "export default class FacebookPageCredentials {\r\n  static forCreator(pageId: string, pageAccessToken: string, creatorId: string): FacebookPageCredentials {\r\n    return new FacebookPageCredentials(pageId, pageAccessToken, creatorId);\r\n  }\r\n\r\n  static forInterestedCreator(pageId: string, pageAccessToken: string, nucleusId: string): FacebookPageCredentials {\r\n    return new FacebookPageCredentials(pageId, pageAccessToken, null, nucleusId);\r\n  }\r\n\r\n  private constructor(\r\n    readonly pageId: string,\r\n    readonly pageAccessToken: string,\r\n    readonly creatorId: string,\r\n    readonly nucleusId?: string\r\n  ) {}\r\n}\r\n"}, "src/server/channels/facebook/ViewFacebookPagesController.ts": {"language": "typescript", "mutants": [{"id": "37", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/channels/facebook/ViewFacebookPagesController.ts(14,3): error TS2377: Constructors for derived classes must contain a 'super' call.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 16}, "start": {"column": 66, "line": 14}}}, {"id": "38", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 27}, "start": {"column": 85, "line": 18}}}, {"id": "39", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 46, "line": 19}, "start": {"column": 37, "line": 19}}}, {"id": "40", "mutatorName": "<PERSON>olean<PERSON>iter<PERSON>", "replacement": "pages", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 15, "line": 21}, "start": {"column": 9, "line": 21}}}, {"id": "41", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 15, "line": 21}, "start": {"column": 9, "line": 21}}}, {"id": "42", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 15, "line": 21}, "start": {"column": 9, "line": 21}}}, {"id": "43", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 24}, "start": {"column": 17, "line": 21}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport {\r\n  Controller,\r\n  NextApiRequestWithSession,\r\n  RequestHandler,\r\n  type RequestHandlerOptions\r\n} from \"@eait-playerexp-cn/server-kernel\";\r\nimport { ApiProblem } from \"@eait-playerexp-cn/api-problem\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\n\r\n@Service()\r\nclass ViewFacebookPagesController extends Request<PERSON><PERSON>ler implements Controller {\r\n  constructor(@Inject(\"options\") options: RequestHandlerOptions) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const pages = this.session(req, \"fbPages\");\r\n\r\n    if (!pages) {\r\n      this.json(res, ApiProblem.from(HttpStatus.NOT_FOUND), HttpStatus.NOT_FOUND);\r\n      return;\r\n    }\r\n\r\n    this.json(res, pages);\r\n  }\r\n}\r\n\r\nexport default ViewFacebookPagesController;\r\n"}, "src/server/connecteAccounts/ClearAccountTypeController.ts": {"language": "typescript", "mutants": [{"id": "44", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ClearAccountTypeController.ts(13,3): error TS2377: Constructors for derived classes must contain a 'super' call.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["25"], "location": {"end": {"column": 4, "line": 15}, "start": {"column": 66, "line": 13}}}, {"id": "45", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/ClearAccountTypeController.spec.ts:27:30)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["25"], "coveredBy": ["25"], "location": {"end": {"column": 4, "line": 20}, "start": {"column": 85, "line": 17}}}, {"id": "46", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["25"], "location": {"end": {"column": 52, "line": 18}, "start": {"column": 39, "line": 18}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport {\r\n  Controller,\r\n  NextApiRequestWithSession,\r\n  RequestHandler,\r\n  type RequestHandlerOptions\r\n} from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\n\r\n@Service()\r\nclass ClearAccountTypeController extends RequestHandler implements Controller {\r\n  constructor(@Inject(\"options\") options: RequestHandlerOptions) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    await this.removeFromSession(req, \"accountType\");\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default ClearAccountTypeController;\r\n"}, "src/server/connecteAccounts/ClearSessionsKeyController.ts": {"language": "typescript", "mutants": [{"id": "47", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ClearSessionsKeyController.ts(13,3): error TS2377: Constructors for derived classes must contain a 'super' call.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["24"], "location": {"end": {"column": 4, "line": 15}, "start": {"column": 66, "line": 13}}}, {"id": "48", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/ClearSessionsKeyController.spec.ts:28:30)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["24"], "coveredBy": ["24"], "location": {"end": {"column": 4, "line": 21}, "start": {"column": 85, "line": 17}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport {\r\n  Controller,\r\n  NextApiRequestWithSession,\r\n  RequestHandler,\r\n  type RequestHandlerOptions\r\n} from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\n\r\n@Service()\r\nclass ClearSessionsKeyController extends RequestHandler implements Controller {\r\n  constructor(@Inject(\"options\") options: RequestHandlerOptions) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const sessionKey = req.query.key as string;\r\n    await this.removeFromSession(req, sessionKey);\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default ClearSessionsKeyController;\r\n"}, "src/server/connecteAccounts/ConnectedAccountsHttpClient.ts": {"language": "typescript", "mutants": [{"id": "49", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ConnectedAccountsHttpClient.ts(33,53): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["6"], "location": {"end": {"column": 4, "line": 36}, "start": {"column": 86, "line": 33}}}, {"id": "50", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"/v1/connected-accounts/abc124476458fhgfghl\"\nReceived: \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/ea/onboarding-micro-frontend/strykerTmp/sandbox7438528/__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts:29:24)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["6"], "coveredBy": ["6"], "location": {"end": {"column": 96, "line": 34}, "start": {"column": 59, "line": 34}}}, {"id": "51", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ConnectedAccountsHttpClient.ts(41,73): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 44}, "start": {"column": 106, "line": 41}}}, {"id": "52", "mutatorName": "StringLiteral", "replacement": "``", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 96, "line": 42}, "start": {"column": 59, "line": 42}}}, {"id": "53", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 52}, "start": {"column": 77, "line": 49}}}, {"id": "54", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 50, "line": 50}, "start": {"column": 28, "line": 50}}}, {"id": "55", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 73, "line": 50}, "start": {"column": 52, "line": 50}}}, {"id": "56", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 60}, "start": {"column": 76, "line": 57}}}, {"id": "57", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 49, "line": 58}, "start": {"column": 28, "line": 58}}}, {"id": "58", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 72, "line": 58}, "start": {"column": 51, "line": 58}}}, {"id": "59", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ConnectedAccountsHttpClient.ts(65,38): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 68}, "start": {"column": 67, "line": 65}}}, {"id": "60", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 79, "line": 66}, "start": {"column": 59, "line": 66}}}, {"id": "61", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 100, "line": 66}, "start": {"column": 81, "line": 66}}}, {"id": "62", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 98, "line": 66}, "start": {"column": 90, "line": 66}}}, {"id": "63", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (/Users/<USER>/ea/onboarding-micro-frontend/strykerTmp/sandbox7438528/__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts:46:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["7"], "coveredBy": ["7"], "location": {"end": {"column": 4, "line": 76}, "start": {"column": 82, "line": 73}}}, {"id": "64", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- \"/v1/facebook-accounts\",\n+ \"\",\n  {\"body\": {\"accessToken\": \"f\", \"creatorId\": null, \"nucleusId\": \"abc124476458fhgfghl\", \"pageAccessToken\": \"w\", \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\"}},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/ea/onboarding-micro-frontend/strykerTmp/sandbox7438528/__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts:47:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["7"], "coveredBy": ["7"], "location": {"end": {"column": 51, "line": 74}, "start": {"column": 28, "line": 74}}}, {"id": "65", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v1/facebook-accounts\",\n- Object {\n-   \"body\": Object {\n-     \"accessToken\": \"I\",\n-     \"creatorId\": null,\n-     \"nucleusId\": \"abc124476458fhgfghl\",\n-     \"pageAccessToken\": \"q\",\n-     \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\",\n-   },\n- }\n+ Object {},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/ea/onboarding-micro-frontend/strykerTmp/sandbox7438528/__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts:47:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["7"], "coveredBy": ["7"], "location": {"end": {"column": 74, "line": 74}, "start": {"column": 53, "line": 74}}}, {"id": "66", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 84}, "start": {"column": 79, "line": 81}}}, {"id": "67", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 52, "line": 82}, "start": {"column": 28, "line": 82}}}, {"id": "68", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 75, "line": 82}, "start": {"column": 54, "line": 82}}}, {"id": "69", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 92}, "start": {"column": 76, "line": 89}}}, {"id": "70", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 49, "line": 90}, "start": {"column": 28, "line": 90}}}, {"id": "71", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 72, "line": 90}, "start": {"column": 51, "line": 90}}}, {"id": "72", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (/Users/<USER>/ea/onboarding-micro-frontend/strykerTmp/sandbox7438528/__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts:58:27)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["8"], "coveredBy": ["8"], "location": {"end": {"column": 4, "line": 100}, "start": {"column": 73, "line": 97}}}, {"id": "73", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- \"/v1/connected-accounts/a1LDF00000K4z0i2AB\",\n+ \"\",\n  {\"query\": {\"type\": \"INTERESTED_CREATOR\"}},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/ea/onboarding-micro-frontend/strykerTmp/sandbox7438528/__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts:59:27)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["8"], "coveredBy": ["8"], "location": {"end": {"column": 60, "line": 98}, "start": {"column": 30, "line": 98}}}, {"id": "74", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v1/connected-accounts/a1LDF00000K4z0i2AB\",\n- Object {\n-   \"query\": Object {\n-     \"type\": \"INTERESTED_CREATOR\",\n-   },\n- }\n+ Object {},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/ea/onboarding-micro-frontend/strykerTmp/sandbox7438528/__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts:59:27)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["8"], "coveredBy": ["8"], "location": {"end": {"column": 81, "line": 98}, "start": {"column": 62, "line": 98}}}, {"id": "75", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v1/connected-accounts/a1LDF00000K4z0i2AB\",\n  Object {\n-   \"query\": Object {\n-     \"type\": \"INTERESTED_CREATOR\",\n-   },\n+   \"query\": Object {},\n  },\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/ea/onboarding-micro-frontend/strykerTmp/sandbox7438528/__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts:59:27)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["8"], "coveredBy": ["8"], "location": {"end": {"column": 79, "line": 98}, "start": {"column": 71, "line": 98}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport { AxiosResponse } from \"axios\";\r\nimport FacebookPageCredentials from \"../channels/facebook/FacebookPageCredentials\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\n\r\nexport type FacebookPage = {\r\n  id: string;\r\n  accessToken: string;\r\n  name: string;\r\n};\r\n\r\nexport type ChannelType = \"YOUTUBE\" | \"INSTAGRAM\" | \"TWITCH\" | \"FACEBOOK\" | \"TIKTOK\";\r\n\r\nexport type ConnectedAccount = {\r\n  id: string;\r\n  accountId: string;\r\n  name: string;\r\n  type: ChannelType;\r\n  uri: string;\r\n  thumbnail: string;\r\n  username: string;\r\n  disconnected: boolean;\r\n  isExpired: boolean;\r\n};\r\n\r\n@Service()\r\nclass ConnectedAccountsHttpClient {\r\n  constructor(@Inject(\"contentSubmissionClient\") private readonly client: TraceableHttpClient) {}\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/viewInterestedCreatorConnectedAccounts}\r\n   */\r\n  async getAllConnectedAccounts(nucleusId: string): Promise<Array<ConnectedAccount>> {\r\n    const response: AxiosResponse = await this.client.get(`/v1/connected-accounts/${nucleusId}`);\r\n    return Promise.resolve(response.data);\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Social-Channels/operation/viewInterestedCreatorConnectedAccountsV2}\r\n   */\r\n  async getAllConnectedAccountsWithExpirationStatus(nucleusId: string): Promise<Array<ConnectedAccount>> {\r\n    const response: AxiosResponse = await this.client.get(`/v2/connected-accounts/${nucleusId}`);\r\n    return Promise.resolve(response.data);\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveYoutubeAccount}\r\n   */\r\n  async connectYouTubeAccount(credentials: ConnectedAccount): Promise<void> {\r\n    await this.client.post(\"/v1/youtube-accounts\", { body: credentials });\r\n    return Promise.resolve();\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveTwitchAccount}\r\n   */\r\n  async connectTwitchAccount(credentials: ConnectedAccount): Promise<void> {\r\n    await this.client.post(\"/v1/twitch-accounts\", { body: credentials });\r\n    return Promise.resolve();\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/viewFacebookPages}\r\n   */\r\n  async facebookPages(code: string): Promise<Array<FacebookPage>> {\r\n    const response: AxiosResponse = await this.client.get(\"/v1/facebook-pages\", { query: { code } });\r\n    return Promise.resolve(response.data);\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveFacebookAccount}\r\n   */\r\n  async connectFacebookPage(credentials: FacebookPageCredentials): Promise<void> {\r\n    await this.client.post(\"/v1/facebook-accounts\", { body: credentials });\r\n    return Promise.resolve();\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveInstagramAccount}\r\n   */\r\n  async connectInstagramAccount(credentials: ConnectedAccount): Promise<void> {\r\n    await this.client.post(\"/v1/instagram-accounts\", { body: credentials });\r\n    return Promise.resolve();\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveTikTokAccountV2}\r\n   */\r\n  async connectTikTokAccount(credentials: ConnectedAccount): Promise<void> {\r\n    await this.client.post(\"/v2/tiktok-accounts\", { body: credentials });\r\n    return Promise.resolve();\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/disconnectAccount}\r\n   */\r\n  async removeConnectedAccount(id: string, type: string): Promise<void> {\r\n    await this.client.delete(`/v1/connected-accounts/${id}`, { query: { type } });\r\n    return Promise.resolve();\r\n  }\r\n}\r\n\r\nexport default ConnectedAccountsHttpClient;\r\n"}, "src/server/connecteAccounts/RemoveConnectedAccountController.ts": {"language": "typescript", "mutants": [{"id": "76", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/RemoveConnectedAccountController.spec.ts:30:45)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["11"], "coveredBy": ["11", "12"], "location": {"end": {"column": 4, "line": 26}, "start": {"column": 85, "line": 21}}}, {"id": "77", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"a1LDF00000K4z0i2AB\", \"INTERESTED_CREATOR\"\nReceived: \"a1LDF00000K4z0i2AB\", \"CREATOR\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/RemoveConnectedAccountController.spec.ts:31:45)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["11"], "coveredBy": ["11", "12"], "location": {"end": {"column": 55, "line": 22}, "start": {"column": 44, "line": 22}}}, {"id": "78", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"a1LDF00000K4z0i2AB\", \"INTERESTED_CREATOR\"\nReceived: \"a1LDF00000K4z0i2AB\", \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/RemoveConnectedAccountController.spec.ts:31:45)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["11"], "coveredBy": ["11"], "location": {"end": {"column": 79, "line": 22}, "start": {"column": 59, "line": 22}}}, {"id": "79", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"a1LDF00000K4z0i2AB\", \"CREATOR\"\nReceived: \"a1LDF00000K4z0i2AB\", \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/RemoveConnectedAccountController.spec.ts:48:45)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["12"], "coveredBy": ["12"], "location": {"end": {"column": 91, "line": 22}, "start": {"column": 82, "line": 22}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport { NextApiResponse } from \"next\";\r\nimport {\r\n  Controller,\r\n  NextApiRequestWithSession,\r\n  RequestHandler,\r\n  type RequestHandlerOptions\r\n} from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\nimport ConnectedAccountsHttpClient from \"./ConnectedAccountsHttpClient\";\r\n\r\n@Service()\r\nclass RemoveConnectedAccountController extends Request<PERSON><PERSON><PERSON> implements Controller {\r\n  constructor(\r\n    @Inject(\"options\") options: RequestHandlerOptions,\r\n    private readonly connectedAccounts: ConnectedAccountsHttpClient\r\n  ) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const type: string = this.session(req, \"nucleusId\") ? \"INTERESTED_CREATOR\" : \"CREATOR\";\r\n    const accountId = req.query.id as string;\r\n    await this.connectedAccounts.removeConnectedAccount(accountId, type);\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default RemoveConnectedAccountController;\r\n"}, "src/server/connecteAccounts/ViewConnectedAccountsController.ts": {"language": "typescript", "mutants": [{"id": "80", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at Object.parse (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/ViewConnectedAccountsController.spec.ts:47:17)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["4"], "coveredBy": ["4", "5"], "location": {"end": {"column": 4, "line": 30}, "start": {"column": 85, "line": 21}}}, {"id": "81", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: \"abc124444cfhfkflddfgfh\"\nReceived: undefined\n    at Object.toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/ViewConnectedAccountsController.spec.ts:50:35)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["4"], "coveredBy": ["4", "5"], "location": {"end": {"column": 45, "line": 27}, "start": {"column": 34, "line": 27}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport {\r\n  Controller,\r\n  NextApiRequestWithSession,\r\n  RequestHandler,\r\n  type RequestHandlerOptions\r\n} from \"@eait-playerexp-cn/server-kernel\";\r\nimport ConnectedAccountsHttpClient from \"./ConnectedAccountsHttpClient\";\r\nimport config from \"../../../config\";\r\n\r\n@Service()\r\nclass ViewConnectedAccountsController extends Request<PERSON><PERSON>ler implements Controller {\r\n  constructor(\r\n    @Inject(\"options\") options: RequestHandlerOptions,\r\n    private connectedAccounts: ConnectedAccountsHttpClient\r\n  ) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const nucleusId = req.query.nucleusId as string;\r\n    const accounts = config.INTERESTED_CREATOR_REAPPLY_PERIOD\r\n      ? await this.connectedAccounts.getAllConnectedAccountsWithExpirationStatus(nucleusId)\r\n      : await this.connectedAccounts.getAllConnectedAccounts(nucleusId);\r\n\r\n    await this.addToSession(req, \"nucleusId\", nucleusId);\r\n\r\n    this.json(res, accounts);\r\n  }\r\n}\r\n\r\nexport default ViewConnectedAccountsController;\r\n"}, "src/server/connecteAccounts/ViewRecentErrorsController.ts": {"language": "typescript", "mutants": [{"id": "82", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ViewRecentErrorsController.ts(12,3): error TS2377: Constructors for derived classes must contain a 'super' call.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["23"], "location": {"end": {"column": 4, "line": 14}, "start": {"column": 66, "line": 12}}}, {"id": "83", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at Object.parse (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/ViewRecentErrorsController.spec.ts:26:17)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["23"], "coveredBy": ["23"], "location": {"end": {"column": 4, "line": 19}, "start": {"column": 85, "line": 16}}}, {"id": "84", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: {\"code\": \"view-facebook-pages-invalid-input\", \"message\": \"Please try with appropriate permissions\"}\nReceived: null\n    at Object.toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/connecteAccounts/ViewRecentErrorsController.spec.ts:26:40)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["23"], "coveredBy": ["23"], "location": {"end": {"column": 44, "line": 17}, "start": {"column": 37, "line": 17}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport {\r\n  Controller,\r\n  NextApiRequestWithSession,\r\n  RequestHandler,\r\n  type RequestHandlerOptions\r\n} from \"@eait-playerexp-cn/server-kernel\";\r\n\r\n@Service()\r\nclass ViewRecentErrorsController extends RequestHandler implements Controller {\r\n  constructor(@Inject(\"options\") options: RequestHandlerOptions) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const error = this.session(req, \"error\");\r\n    this.json(res, error);\r\n  }\r\n}\r\n\r\nexport default ViewRecentErrorsController;\r\n"}, "src/server/creatorCode/ContentScanningHttpClient.ts": {"language": "typescript", "mutants": [{"id": "85", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/creatorCode/ContentScanningHttpClient.ts(29,75): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 31}, "start": {"column": 117, "line": 29}}}, {"id": "86", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 56, "line": 30}, "start": {"column": 29, "line": 30}}}, {"id": "87", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 90, "line": 30}, "start": {"column": 58, "line": 30}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport { AxiosResponse } from \"axios\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\n\r\nexport type UnreviewedCreatorCode = {\r\n  programCode: string;\r\n  deviceType: string;\r\n  platform: string;\r\n  nucleusId: number;\r\n  contents: Array<string>;\r\n};\r\n\r\nexport type ScannedCreatorCode = {\r\n  content: string;\r\n  healthy: boolean;\r\n};\r\n\r\nexport type ContentScanResult = {\r\n  results: Array<ScannedCreatorCode>;\r\n};\r\n\r\n@Service()\r\nclass ContentScanningHttpClient {\r\n  constructor(@Inject(\"contentScanningClient\") private readonly client: TraceableHttpClient) {}\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#tag/Contents/operation/checkContentHealth Validate Content Creator Code}\r\n   */\r\n  async verifyCreatorCode(creatorCodeRequestBody: UnreviewedCreatorCode): Promise<AxiosResponse<ContentScanResult>> {\r\n    return this.client.post(\"/v1/healthy-text-contents\", { body: creatorCodeRequestBody });\r\n  }\r\n}\r\n\r\nexport default ContentScanningHttpClient;\r\n"}, "src/server/creatorCode/VerifyCreatorCodeController.ts": {"language": "typescript", "mutants": [{"id": "93", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 33}, "start": {"column": 85, "line": 17}}}, {"id": "94", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 27}, "start": {"column": 30, "line": 22}}}, {"id": "95", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/creatorCode/VerifyCreatorCodeController.ts(24,11): error TS2739: Type '{}' is missing the following properties from type 'UnreviewedCreatorCode': programCode, deviceType, platform, nucleusId, contents\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 95, "line": 28}, "start": {"column": 59, "line": 28}}}], "source": "import { AuthenticatedRequestHandler } from \"@eait-playerexp-cn/identity\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport config from \"config\";\r\nimport ContentScanningHttpClient, { UnreviewedCreatorCode } from \"./ContentScanningHttpClient\";\r\n\r\n@Service()\r\nclass VerifyCreatorCodeController extends AuthenticatedRequestHandler implements Controller {\r\n  constructor(\r\n    @Inject(\"options\") options: RequestHandlerOptions,\r\n    private readonly contentScanning: ContentScanningHttpClient\r\n  ) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const nucleusId = this.identity(req).nucleusId;\r\n    const programCode = config.PROGRAM_CODE;\r\n    const deviceType = config.DEVICE_TYPE;\r\n    const platform = config.PLATFORM;\r\n    const additionalValues = {\r\n      nucleusId,\r\n      programCode,\r\n      deviceType,\r\n      platform\r\n    };\r\n    const creatorCodeRequestBody: UnreviewedCreatorCode = { ...req.body, ...additionalValues };\r\n\r\n    const { data } = await this.contentScanning.verifyCreatorCode(creatorCodeRequestBody);\r\n\r\n    this.json(res, data);\r\n  }\r\n}\r\n\r\nexport default VerifyCreatorCodeController;\r\n"}, "src/server/creators/AccountInformationInput.ts": {"language": "typescript", "mutants": [{"id": "96", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 41}, "start": {"column": 68, "line": 29}}}, {"id": "97", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 38, "line": 36}, "start": {"column": 19, "line": 36}}}, {"id": "98", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 38, "line": 36}, "start": {"column": 19, "line": 36}}}, {"id": "99", "mutatorName": "EqualityOperator", "replacement": "creator.id !== null", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 38, "line": 36}, "start": {"column": 19, "line": 36}}}, {"id": "100", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 55, "line": 36}, "start": {"column": 41, "line": 36}}}, {"id": "101", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "src/server/creators/AccountInformationInput.ts(28,5): error TS2322: Type 'string | boolean' is not assignable to type 'string'.\n  Type 'boolean' is not assignable to type 'string'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 79, "line": 36}, "start": {"column": 58, "line": 36}}}, {"id": "102", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "src/server/creators/AccountInformationInput.ts(28,5): error TS2322: Type 'string | boolean' is not assignable to type 'string'.\n  Type 'boolean' is not assignable to type 'string'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 79, "line": 36}, "start": {"column": 58, "line": 36}}}, {"id": "103", "mutatorName": "LogicalOperator", "replacement": "values.status && null", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 79, "line": 36}, "start": {"column": 58, "line": 36}}}], "source": "import { User } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\nexport type AccountInformationFormValues = {\r\n  firstName: string;\r\n  lastName: string;\r\n  dateOfBirth: string;\r\n  nucleusId: number;\r\n  originEmail: string;\r\n  status: string;\r\n  isPayable: boolean;\r\n  creatorCode: string;\r\n  preferredName: string;\r\n  preferredPronouns: string;\r\n};\r\n\r\nexport default class AccountInformationInput {\r\n  readonly firstName: string;\r\n  readonly lastName: string;\r\n  readonly dateOfBirth: string;\r\n  readonly nucleusId: number;\r\n  readonly originEmail: string;\r\n  readonly defaultGamerTag: string;\r\n  readonly status: string;\r\n  readonly isPayable: boolean;\r\n  readonly creatorCode: string;\r\n  readonly preferredName: string;\r\n  readonly preferredPronouns: string;\r\n\r\n  constructor(creator: User, values: AccountInformationFormValues) {\r\n    this.firstName = values.firstName;\r\n    this.lastName = values.lastName;\r\n    this.dateOfBirth = values.dateOfBirth;\r\n    this.nucleusId = values.nucleusId;\r\n    this.originEmail = values.originEmail;\r\n    this.defaultGamerTag = creator.username;\r\n    this.status = creator.id === null ? \"UNREGISTERED\" : values.status || null;\r\n    this.isPayable = values.isPayable;\r\n    this.creatorCode = values.creatorCode;\r\n    this.preferredName = values.preferredName;\r\n    this.preferredPronouns = values.preferredPronouns;\r\n  }\r\n}\r\n"}, "src/server/creators/CreatorsHttpClient.ts": {"language": "typescript", "mutants": [{"id": "104", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/creators/CreatorsHttpClient.ts(22,46): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 26}, "start": {"column": 85, "line": 21}}}, {"id": "105", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 73, "line": 22}, "start": {"column": 59, "line": 22}}}, {"id": "106", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 94, "line": 22}, "start": {"column": 75, "line": 22}}}, {"id": "107", "mutatorName": "MethodExpression", "replacement": "response.data", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 76, "line": 24}, "start": {"column": 12, "line": 23}}}, {"id": "108", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 75, "line": 24}, "start": {"column": 15, "line": 24}}}, {"id": "109", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 75, "line": 24}, "start": {"column": 22, "line": 24}}}, {"id": "110", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 75, "line": 24}, "start": {"column": 22, "line": 24}}}, {"id": "111", "mutatorName": "EqualityOperator", "replacement": "c.accountInformation.nucleusId !== criteria.nucleusId", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 75, "line": 24}, "start": {"column": 22, "line": 24}}}, {"id": "112", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 58, "line": 25}, "start": {"column": 12, "line": 25}}}, {"id": "113", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 30}, "start": {"column": 60, "line": 28}}}, {"id": "114", "mutatorName": "StringLiteral", "replacement": "``", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 71, "line": 29}, "start": {"column": 30, "line": 29}}}, {"id": "115", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 103, "line": 29}, "start": {"column": 73, "line": 29}}}, {"id": "116", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 34}, "start": {"column": 64, "line": 32}}}, {"id": "117", "mutatorName": "StringLiteral", "replacement": "``", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 56, "line": 33}, "start": {"column": 27, "line": 33}}}, {"id": "118", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 76, "line": 33}, "start": {"column": 58, "line": 33}}}], "source": "import { AxiosResponse } from \"axios\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport AvatarUpload from \"@src/server/Avatar/AvatarUpload\";\r\nimport { AccountInformation, CreatorWithProgramCode } from \"@eait-playerexp-cn/onboarding-ui\";\r\nimport CreatorCriteria from \"./CreatorCriteria\";\r\n\r\ntype UpdatedIdentity = {\r\n  id: string;\r\n  accountInformation: AccountInformation & {\r\n    defaultGamerTag: string;\r\n    originEmail: string;\r\n    lastLoginDate: string;\r\n  };\r\n};\r\n\r\n@Service()\r\nclass CreatorsHttpClient {\r\n  constructor(@Inject(\"operationsClient\") protected client: TraceableHttpClient) {}\r\n\r\n  async matching(criteria: CreatorCriteria): Promise<Array<CreatorWithProgramCode>> {\r\n    const response: AxiosResponse = await this.client.get(\"/v7/creators\", { query: criteria });\r\n    return response.data\r\n      .filter((c) => c.accountInformation.nucleusId === criteria.nucleusId)\r\n      .map((data) => CreatorWithProgramCode.fromApi(data));\r\n  }\r\n\r\n  async upload(uploadedImage: AvatarUpload): Promise<void> {\r\n    await this.client.upload(`/v1/creators/${uploadedImage.id}/avatar`, { body: uploadedImage.avatar });\r\n  }\r\n\r\n  async syncIdentity(identity: UpdatedIdentity): Promise<void> {\r\n    await this.client.put(`/v1/creators/${identity.id}`, { body: identity });\r\n  }\r\n}\r\n\r\nexport default CreatorsHttpClient;\r\n"}, "src/server/creators/MailingAddressInput.ts": {"language": "typescript", "mutants": [{"id": "119", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 24}, "start": {"column": 49, "line": 18}}}, {"id": "120", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/creators/MailingAddressInput.ts(19,5): error TS2739: Type '{}' is missing the following properties from type '{ readonly code: string; readonly name: string; }': code, name\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 78, "line": 19}, "start": {"column": 20, "line": 19}}}], "source": "type CountryFormValues = { value: string; label: string };\r\n\r\nexport type MailingAddressFormValues = {\r\n  country: CountryFormValues;\r\n  street: string;\r\n  city: string;\r\n  state: string;\r\n  zipCode: string;\r\n};\r\n\r\nexport default class MailingAddressInput {\r\n  readonly country: { readonly code: string; readonly name: string };\r\n  readonly street: string;\r\n  readonly city: string;\r\n  readonly state: string;\r\n  readonly zipCode: string;\r\n\r\n  constructor(values: MailingAddressFormValues) {\r\n    this.country = { code: values.country.value, name: values.country.label };\r\n    this.street = values.street;\r\n    this.city = values.city;\r\n    this.state = values.state;\r\n    this.zipCode = values.zipCode;\r\n  }\r\n}\r\n"}, "src/server/creators/SaveCreatorProfile/AdditionalInformationInput.ts": {"language": "typescript", "mutants": [{"id": "121", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 10}, "start": {"column": 50, "line": 7}}}, {"id": "122", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 25}, "start": {"column": 56, "line": 22}}}, {"id": "123", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "src/server/creators/SaveCreatorProfile/AdditionalInformationInput.ts(23,5): error TS2322: Type 'boolean' is not assignable to type 'string'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 54, "line": 23}, "start": {"column": 23, "line": 23}}}, {"id": "124", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "src/server/creators/SaveCreatorProfile/AdditionalInformationInput.ts(23,5): error TS2322: Type 'boolean' is not assignable to type 'string'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 54, "line": 23}, "start": {"column": 23, "line": 23}}}, {"id": "125", "mutatorName": "LogicalOperator", "replacement": "values.hoodieSize.value && null", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 54, "line": 23}, "start": {"column": 23, "line": 23}}}, {"id": "126", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 101, "line": 24}, "start": {"column": 57, "line": 24}}}], "source": "type HardwarePartnerFormValues = { value: string; label: string };\r\n\r\nclass HardwarePartnerInput {\r\n  readonly id: string;\r\n  readonly name: string;\r\n\r\n  constructor(values: HardwarePartnerFormValues) {\r\n    this.id = values.value;\r\n    this.name = values.label;\r\n  }\r\n}\r\n\r\nexport type AdditionalInformationFormValues = {\r\n  hoodieSize: { value: string };\r\n  hardwarePartners: HardwarePartnerFormValues[];\r\n};\r\n\r\nexport default class AdditionalInformationInput {\r\n  readonly hoodieSize?: string;\r\n  readonly hardwarePartners: Array<HardwarePartnerInput>;\r\n\r\n  constructor(values: AdditionalInformationFormValues) {\r\n    this.hoodieSize = values.hoodieSize.value || null;\r\n    this.hardwarePartners = values.hardwarePartners.map((values) => new HardwarePartnerInput(values));\r\n  }\r\n}\r\n"}, "src/server/creators/SaveCreatorProfile/CommunicationPreferencesInput.ts": {"language": "typescript", "mutants": [{"id": "127", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 30}, "start": {"column": 59, "line": 16}}}, {"id": "128", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/creators/SaveCreatorProfile/CommunicationPreferencesInput.ts(19,5): error TS2322: Type 'void[]' is not assignable to type '{ code: string; name: string; }[]'.\n  Type 'void' is not assignable to type '{ code: string; name: string; }'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 10, "line": 25}, "start": {"column": 47, "line": 20}}}, {"id": "129", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/creators/SaveCreatorProfile/CommunicationPreferencesInput.ts(19,5): error TS2322: Type '{}[]' is not assignable to type '{ code: string; name: string; }[]'.\n  Type '{}' is missing the following properties from type '{ code: string; name: string; }': code, name\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 12, "line": 24}, "start": {"column": 18, "line": 21}}}, {"id": "130", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "statusReason": "src/server/creators/SaveCreatorProfile/CommunicationPreferencesInput.ts(19,5): error TS2322: Type '{ code: string; name: string; }[] | string[]' is not assignable to type '{ code: string; name: string; }[]'.\n  Type 'string[]' is not assignable to type '{ code: string; name: string; }[]'.\n    Type 'string' is not assignable to type '{ code: string; name: string; }'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 11, "line": 26}, "start": {"column": 9, "line": 26}}}, {"id": "131", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/creators/SaveCreatorProfile/CommunicationPreferencesInput.ts(27,5): error TS2739: Type '{}' is missing the following properties from type '{ code: string; name: string; }': code, name\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 87, "line": 28}, "start": {"column": 9, "line": 28}}}], "source": "type LanguageFormValues = { code: string; name: string } & { value: string; label: string };\r\n\r\nexport type CommunicationPreferencesFormValues = {\r\n  email: string;\r\n  phone: string;\r\n  contentLanguages: LanguageFormValues[];\r\n  preferredLanguage: LanguageFormValues;\r\n};\r\n\r\nexport default class CommunicationPreferencesInput {\r\n  private readonly email: string;\r\n  private phone: string;\r\n  private contentLanguages: Array<{ code: string; name: string }>;\r\n  private preferredLanguage: { code: string; name: string };\r\n\r\n  constructor(values: CommunicationPreferencesFormValues) {\r\n    this.email = values.email;\r\n    this.phone = values.phone;\r\n    this.contentLanguages = values.contentLanguages.length\r\n      ? values.contentLanguages.map((item) => {\r\n          return {\r\n            code: item.value,\r\n            name: item.label\r\n          };\r\n        })\r\n      : [];\r\n    this.preferredLanguage = values.preferredLanguage\r\n      ? { code: values.preferredLanguage.value, name: values.preferredLanguage.label }\r\n      : null;\r\n  }\r\n}\r\n"}, "src/server/creators/SaveCreatorProfile/CreatorsTypeInput.ts": {"language": "typescript", "mutants": [{"id": "132", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 8}, "start": {"column": 47, "line": 6}}}, {"id": "133", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 54, "line": 7}, "start": {"column": 30, "line": 7}}}], "source": "export type CreatorsTypeFormValues = { value: string }[];\r\n\r\nexport default class CreatorsTypeInput {\r\n  readonly values: Array<string>;\r\n\r\n  constructor(values: CreatorsTypeFormValues) {\r\n    this.values = values.map((values) => values.value);\r\n  }\r\n}\r\n"}, "src/server/creators/SaveCreatorProfile/InvalidProfileData.ts": {"language": "typescript", "mutants": [{"id": "134", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/creators/SaveCreatorProfile/InvalidProfileData.ts(3,3): error TS2377: Constructors for derived classes must contain a 'super' call.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 7}, "start": {"column": 44, "line": 3}}}, {"id": "135", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/creators/SaveCreatorProfile/InvalidProfileData.ts(8,41): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 10}, "start": {"column": 60, "line": 8}}}, {"id": "136", "mutatorName": "StringLiteral", "replacement": "``", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 104, "line": 9}, "start": {"column": 39, "line": 9}}}], "source": "export default class InvalidProfileData extends Error {\r\n  public readonly id: string;\r\n  constructor(id: string, message: string) {\r\n    super(message);\r\n    this.id = id;\r\n    this.message = message;\r\n  }\r\n  public static forCreator(id: string): InvalidProfileData {\r\n    return new InvalidProfileData(id, `Sufficient creator information not found to save for ID '${id}'`);\r\n  }\r\n}\r\n"}, "src/server/creators/SaveCreatorProfile/LegalEntityInformationInput.ts": {"language": "typescript", "mutants": [{"id": "137", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 31}, "start": {"column": 57, "line": 23}}}, {"id": "138", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "src/server/creators/SaveCreatorProfile/LegalEntityInformationInput.ts(24,5): error TS2322: Type 'boolean' is not assignable to type 'string'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 108, "line": 24}, "start": {"column": 23, "line": 24}}}, {"id": "139", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "src/server/creators/SaveCreatorProfile/LegalEntityInformationInput.ts(24,5): error TS2322: Type 'boolean' is not assignable to type 'string'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 108, "line": 24}, "start": {"column": 23, "line": 24}}}, {"id": "140", "mutatorName": "LogicalOperator", "replacement": "(values?.entityType as EntityTypeFormValues)?.value && values?.entityType as string", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 108, "line": 24}, "start": {"column": 23, "line": 24}}}, {"id": "141", "mutatorName": "OptionalChaining", "replacement": "(values?.entityType as EntityTypeFormValues).value", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 74, "line": 24}, "start": {"column": 23, "line": 24}}}, {"id": "142", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 57, "line": 25}, "start": {"column": 25, "line": 25}}}, {"id": "143", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 57, "line": 25}, "start": {"column": 25, "line": 25}}}, {"id": "144", "mutatorName": "EqualityOperator", "replacement": "this.entityType !== \"INDIVIDUAL\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 57, "line": 25}, "start": {"column": 25, "line": 25}}}, {"id": "145", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 57, "line": 25}, "start": {"column": 45, "line": 25}}}, {"id": "146", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/creators/SaveCreatorProfile/LegalEntityInformationInput.ts(28,5): error TS2739: Type '{}' is missing the following properties from type '{ readonly code: string; readonly name: string; }': code, name\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 82, "line": 28}, "start": {"column": 20, "line": 28}}}, {"id": "147", "mutatorName": "OptionalChaining", "replacement": "values?.country.value", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 50, "line": 28}, "start": {"column": 28, "line": 28}}}, {"id": "148", "mutatorName": "OptionalChaining", "replacement": "values.country", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 43, "line": 28}, "start": {"column": 28, "line": 28}}}, {"id": "149", "mutatorName": "OptionalChaining", "replacement": "values?.country.label", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 80, "line": 28}, "start": {"column": 58, "line": 28}}}, {"id": "150", "mutatorName": "OptionalChaining", "replacement": "values.country", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 73, "line": 28}, "start": {"column": 58, "line": 28}}}], "source": "type EntityTypeFormValues = { value: string };\r\n\r\nexport type LegalEntityInformationFormValues = {\r\n  entityType: EntityTypeFormValues | string;\r\n  businessName: string;\r\n  street: string;\r\n  city: string;\r\n  country: { value: string; label: string };\r\n  state: string;\r\n  zipCode: string;\r\n};\r\n\r\nexport default class LegalEntityInformationInput {\r\n  readonly entityType: string;\r\n  readonly businessName?: string;\r\n  readonly street: string;\r\n  readonly addressLine2?: string;\r\n  readonly city: string;\r\n  public country: { readonly code: string; readonly name: string };\r\n  readonly state: string;\r\n  readonly zipCode: string;\r\n\r\n  constructor(values: LegalEntityInformationFormValues) {\r\n    this.entityType = (values?.entityType as EntityTypeFormValues)?.value || (values?.entityType as string);\r\n    this.businessName = this.entityType === \"INDIVIDUAL\" ? null : values.businessName;\r\n    this.street = values.street;\r\n    this.city = values.city;\r\n    this.country = { code: values?.country?.value, name: values?.country?.label };\r\n    this.state = values.state;\r\n    this.zipCode = values.zipCode;\r\n  }\r\n}\r\n"}, "src/server/creators/SaveCreatorProfile/PreferredFranchisesInput.ts": {"language": "typescript", "mutants": [{"id": "151", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 13}, "start": {"column": 5, "line": 10}}}, {"id": "152", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 29}, "start": {"column": 54, "line": 24}}}, {"id": "153", "mutatorName": "MethodExpression", "replacement": "values.secondaryFranchise", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 38, "line": 26}, "start": {"column": 19, "line": 25}}}, {"id": "154", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 37, "line": 26}, "start": {"column": 15, "line": 26}}}, {"id": "155", "mutatorName": "<PERSON>olean<PERSON>iter<PERSON>", "replacement": "!label", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 37, "line": 26}, "start": {"column": 30, "line": 26}}}, {"id": "156", "mutatorName": "<PERSON>olean<PERSON>iter<PERSON>", "replacement": "label", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 37, "line": 26}, "start": {"column": 31, "line": 26}}}, {"id": "157", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 67, "line": 27}, "start": {"column": 12, "line": 27}}}, {"id": "158", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 66, "line": 27}, "start": {"column": 55, "line": 27}}}, {"id": "159", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 79, "line": 28}, "start": {"column": 70, "line": 28}}}], "source": "type PreferredFranchiseFormValues = { value: string; label: string };\r\n\r\nclass PreferredFranchise {\r\n  readonly id: string;\r\n  readonly name: string;\r\n\r\n  constructor(\r\n    values: PreferredFranchiseFormValues,\r\n    readonly type: string\r\n  ) {\r\n    this.id = values.value;\r\n    this.name = values.label;\r\n  }\r\n}\r\n\r\nexport type PreferredFranchisesFormValues = {\r\n  primaryFranchise: PreferredFranchiseFormValues;\r\n  secondaryFranchise: PreferredFranchiseFormValues[];\r\n};\r\n\r\nexport default class PreferredFranchisesInput {\r\n  readonly values: PreferredFranchise[];\r\n\r\n  constructor(values: PreferredFranchisesFormValues) {\r\n    this.values = values.secondaryFranchise\r\n      .filter(({ label }) => !!label)\r\n      .map((values) => new PreferredFranchise(values, \"SECONDARY\"));\r\n    this.values.push(new PreferredFranchise(values.primaryFranchise, \"PRIMARY\"));\r\n  }\r\n}\r\n"}, "src/server/creators/SaveCreatorProfile/PreferredPlatformsInput.ts": {"language": "typescript", "mutants": [{"id": "160", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 13}, "start": {"column": 5, "line": 10}}}, {"id": "161", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 27}, "start": {"column": 53, "line": 24}}}, {"id": "162", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 103, "line": 25}, "start": {"column": 49, "line": 25}}}, {"id": "163", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 102, "line": 25}, "start": {"column": 91, "line": 25}}}, {"id": "164", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 77, "line": 26}, "start": {"column": 68, "line": 26}}}], "source": "type PreferredPlatformFormValues = { value: string; label: string };\r\n\r\nclass PreferredPlatform {\r\n  readonly id: string;\r\n  readonly name: string;\r\n\r\n  constructor(\r\n    values: PreferredPlatformFormValues,\r\n    readonly type: string\r\n  ) {\r\n    this.id = values.value;\r\n    this.name = values.label;\r\n  }\r\n}\r\n\r\nexport type PreferredPlatformsFormValues = {\r\n  primaryPlatform: PreferredPlatformFormValues;\r\n  secondaryPlatforms: PreferredPlatformFormValues[];\r\n};\r\n\r\nexport default class PreferredPlatformsInput {\r\n  readonly values: PreferredPlatform[];\r\n\r\n  constructor(values: PreferredPlatformsFormValues) {\r\n    this.values = values.secondaryPlatforms.map((values) => new PreferredPlatform(values, \"SECONDARY\"));\r\n    this.values.push(new PreferredPlatform(values.primaryPlatform, \"PRIMARY\"));\r\n  }\r\n}\r\n"}, "src/server/creators/SaveCreatorProfile/SaveCreatorProfileInput.ts": {"language": "typescript", "mutants": [{"id": "165", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "statusReason": "src/server/creators/SaveCreatorProfile/SaveCreatorProfileInput.ts(20,71): error TS2322: Type 'string' is not assignable to type '{ id: string; type: string; }'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 72, "line": 18}, "start": {"column": 70, "line": 18}}}, {"id": "166", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "statusReason": "src/server/creators/SaveCreatorProfile/SaveCreatorProfileInput.ts(21,72): error TS2322: Type 'string' is not assignable to type '{ id: string; type: string; }'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 73, "line": 19}, "start": {"column": 71, "line": 19}}}, {"id": "167", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 50, "line": 21}, "start": {"column": 48, "line": 21}}}, {"id": "168", "mutatorName": "StringLiteral", "replacement": "\"<PERSON><PERSON><PERSON> was here!\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 48, "line": 22}, "start": {"column": 46, "line": 22}}}, {"id": "169", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 84}, "start": {"column": 5, "line": 42}}}, {"id": "170", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "src/server/creators/SaveCreatorProfile/SaveCreatorProfileInput.ts(39,5): error TS2322: Type 'boolean' is not assignable to type 'string'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 33, "line": 43}, "start": {"column": 15, "line": 43}}}, {"id": "171", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "src/server/creators/SaveCreatorProfile/SaveCreatorProfileInput.ts(39,5): error TS2322: Type 'boolean' is not assignable to type 'string'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 33, "line": 43}, "start": {"column": 15, "line": 43}}}, {"id": "172", "mutatorName": "LogicalOperator", "replacement": "creator.id && null", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 33, "line": 43}, "start": {"column": 15, "line": 43}}}, {"id": "173", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 52, "line": 45}, "start": {"column": 9, "line": 45}}}, {"id": "174", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 52, "line": 45}, "start": {"column": 9, "line": 45}}}, {"id": "175", "mutatorName": "EqualityOperator", "replacement": "values.creatorConnectedProgram == undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 52, "line": 45}, "start": {"column": 9, "line": 45}}}, {"id": "176", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 47}, "start": {"column": 54, "line": 45}}}, {"id": "177", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 47, "line": 49}, "start": {"column": 9, "line": 49}}}, {"id": "178", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 47, "line": 49}, "start": {"column": 9, "line": 49}}}, {"id": "179", "mutatorName": "EqualityOperator", "replacement": "values.creatorSocialLinks == undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 47, "line": 49}, "start": {"column": 9, "line": 49}}}, {"id": "180", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 51}, "start": {"column": 49, "line": 49}}}, {"id": "181", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 48, "line": 53}, "start": {"column": 9, "line": 53}}}, {"id": "182", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 48, "line": 53}, "start": {"column": 9, "line": 53}}}, {"id": "183", "mutatorName": "EqualityOperator", "replacement": "values.accountInformation === undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 48, "line": 53}, "start": {"column": 9, "line": 53}}}, {"id": "184", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 55}, "start": {"column": 50, "line": 53}}}, {"id": "185", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 44, "line": 56}, "start": {"column": 9, "line": 56}}}, {"id": "186", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 44, "line": 56}, "start": {"column": 9, "line": 56}}}, {"id": "187", "mutatorName": "EqualityOperator", "replacement": "values.mailingAddress === undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 44, "line": 56}, "start": {"column": 9, "line": 56}}}, {"id": "188", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 58}, "start": {"column": 46, "line": 56}}}, {"id": "189", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 51, "line": 59}, "start": {"column": 9, "line": 59}}}, {"id": "190", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 51, "line": 59}, "start": {"column": 9, "line": 59}}}, {"id": "191", "mutatorName": "EqualityOperator", "replacement": "values.additionalInformation === undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 51, "line": 59}, "start": {"column": 9, "line": 59}}}, {"id": "192", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 61}, "start": {"column": 53, "line": 59}}}, {"id": "193", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 46, "line": 62}, "start": {"column": 9, "line": 62}}}, {"id": "194", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 46, "line": 62}, "start": {"column": 9, "line": 62}}}, {"id": "195", "mutatorName": "EqualityOperator", "replacement": "values.legalInformation === undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 46, "line": 62}, "start": {"column": 9, "line": 62}}}, {"id": "196", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 64}, "start": {"column": 48, "line": 62}}}, {"id": "197", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 41, "line": 65}, "start": {"column": 9, "line": 65}}}, {"id": "198", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 41, "line": 65}, "start": {"column": 9, "line": 65}}}, {"id": "199", "mutatorName": "EqualityOperator", "replacement": "values.information === undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 41, "line": 65}, "start": {"column": 9, "line": 65}}}, {"id": "200", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 70}, "start": {"column": 43, "line": 65}}}, {"id": "201", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "statusReason": "src/server/creators/SaveCreatorProfile/SaveCreatorProfileInput.ts(70,34): error TS2322: Type 'string' is not assignable to type '{ id: string; type: string; }'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 35, "line": 68}, "start": {"column": 33, "line": 68}}}, {"id": "202", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "statusReason": "src/server/creators/SaveCreatorProfile/SaveCreatorProfileInput.ts(73,32): error TS2322: Type 'string' is not assignable to type '{ id: string; type: string; }'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 33, "line": 71}, "start": {"column": 31, "line": 71}}}, {"id": "203", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 42, "line": 73}, "start": {"column": 9, "line": 73}}}, {"id": "204", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 42, "line": 73}, "start": {"column": 9, "line": 73}}}, {"id": "205", "mutatorName": "EqualityOperator", "replacement": "values.creatorTypes === undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 42, "line": 73}, "start": {"column": 9, "line": 73}}}, {"id": "206", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 75}, "start": {"column": 44, "line": 73}}}, {"id": "207", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 47, "line": 77}, "start": {"column": 9, "line": 77}}}, {"id": "208", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 47, "line": 77}, "start": {"column": 9, "line": 77}}}, {"id": "209", "mutatorName": "EqualityOperator", "replacement": "values.franchisesYouPlay === undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 47, "line": 77}, "start": {"column": 9, "line": 77}}}, {"id": "210", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 79}, "start": {"column": 49, "line": 77}}}, {"id": "211", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 54, "line": 81}, "start": {"column": 9, "line": 81}}}, {"id": "212", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 54, "line": 81}, "start": {"column": 9, "line": 81}}}, {"id": "213", "mutatorName": "EqualityOperator", "replacement": "values.communicationPreferences === undefined", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 54, "line": 81}, "start": {"column": 9, "line": 81}}}, {"id": "214", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 83}, "start": {"column": 56, "line": 81}}}, {"id": "215", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/creators/SaveCreatorProfile/SaveCreatorProfileInput.ts(75,12): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 88}, "start": {"column": 20, "line": 86}}}, {"id": "216", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 28, "line": 87}, "start": {"column": 12, "line": 87}}}, {"id": "217", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 28, "line": 87}, "start": {"column": 12, "line": 87}}}, {"id": "218", "mutatorName": "EqualityOperator", "replacement": "this.id !== null", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 28, "line": 87}, "start": {"column": 12, "line": 87}}}, {"id": "219", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 92}, "start": {"column": 27, "line": 90}}}], "source": "import CommunicationPreferencesInput, { CommunicationPreferencesFormValues } from \"./CommunicationPreferencesInput\";\r\nimport PreferredFranchisesInput, { PreferredFranchisesFormValues } from \"./PreferredFranchisesInput\";\r\nimport { PreferredPlatformsFormValues } from \"./PreferredPlatformsInput\";\r\nimport CreatorsTypeInput, { CreatorsTypeFormValues } from \"./CreatorsTypeInput\";\r\nimport AdditionalInformationInput, { AdditionalInformationFormValues } from \"./AdditionalInformationInput\";\r\nimport LegalEntityInformationInput, { LegalEntityInformationFormValues } from \"./LegalEntityInformationInput\";\r\nimport AccountInformationInput, { AccountInformationFormValues } from \"../AccountInformationInput\";\r\nimport { User } from \"@eait-playerexp-cn/server-kernel\";\r\nimport MailingAddressInput, { MailingAddressFormValues } from \"../MailingAddressInput\";\r\n\r\nexport default class SaveCreatorProfileInput {\r\n  public id?: string;\r\n  readonly creatorTypes?: Array<string> = null;\r\n  readonly accountInformation?: AccountInformationInput = null;\r\n  readonly mailingAddress?: MailingAddressInput = null;\r\n  readonly communicationPreferences?: CommunicationPreferencesInput = null;\r\n  readonly additionalInformation?: AdditionalInformationInput = null;\r\n  readonly preferredPlatforms: Array<{ id: string; type: string }> = [];\r\n  readonly preferredFranchises: Array<{ id: string; type: string }> = [];\r\n  readonly legalInformation?: LegalEntityInformationInput;\r\n  readonly creatorSocialLinks: Array<string> = [];\r\n  readonly creatorConnectedProgram: string = \"\";\r\n\r\n  constructor(\r\n    creator: User,\r\n    values: {\r\n      readonly accountInformation: AccountInformationFormValues;\r\n      readonly mailingAddress: MailingAddressFormValues;\r\n      readonly legalInformation: LegalEntityInformationFormValues;\r\n      readonly additionalInformation: AdditionalInformationFormValues;\r\n      readonly information: AccountInformationFormValues &\r\n        MailingAddressFormValues &\r\n        PreferredPlatformsFormValues & { creatorConnectedProgram: string };\r\n      readonly franchisesYouPlay: PreferredFranchisesFormValues;\r\n      readonly platformPreferences: PreferredPlatformsFormValues;\r\n      readonly creatorTypes: CreatorsTypeFormValues;\r\n      readonly communicationPreferences: CommunicationPreferencesFormValues;\r\n      readonly creatorSocialLinks: Array<string>;\r\n      readonly creatorConnectedProgram: string;\r\n    },\r\n    readonly registerCode: string = null\r\n  ) {\r\n    this.id = creator.id || null;\r\n\r\n    if (values.creatorConnectedProgram != undefined) {\r\n      this.creatorConnectedProgram = values.creatorConnectedProgram;\r\n    }\r\n\r\n    if (values.creatorSocialLinks != undefined) {\r\n      this.creatorSocialLinks = values.creatorSocialLinks;\r\n    }\r\n\r\n    if (values.accountInformation !== undefined) {\r\n      this.accountInformation = new AccountInformationInput(creator, values.accountInformation);\r\n    }\r\n    if (values.mailingAddress !== undefined) {\r\n      this.mailingAddress = new MailingAddressInput(values.mailingAddress);\r\n    }\r\n    if (values.additionalInformation !== undefined) {\r\n      this.additionalInformation = new AdditionalInformationInput(values.additionalInformation);\r\n    }\r\n    if (values.legalInformation !== undefined) {\r\n      this.legalInformation = new LegalEntityInformationInput(values.legalInformation);\r\n    }\r\n    if (values.information !== undefined) {\r\n      this.accountInformation = new AccountInformationInput(creator, values.information);\r\n      this.mailingAddress = new MailingAddressInput(values.information);\r\n      this.preferredPlatforms = [];\r\n      this.creatorConnectedProgram = values.information.creatorConnectedProgram;\r\n    }\r\n    this.preferredPlatforms = [];\r\n\r\n    if (values.creatorTypes !== undefined) {\r\n      this.creatorTypes = new CreatorsTypeInput(values.creatorTypes).values;\r\n    }\r\n\r\n    if (values.franchisesYouPlay !== undefined) {\r\n      this.preferredFranchises = new PreferredFranchisesInput(values.franchisesYouPlay).values;\r\n    }\r\n\r\n    if (values.communicationPreferences !== undefined) {\r\n      this.communicationPreferences = new CommunicationPreferencesInput(values.communicationPreferences);\r\n    }\r\n  }\r\n\r\n  isNew(): boolean {\r\n    return this.id === null;\r\n  }\r\n\r\n  setId(id: string): void {\r\n    this.id = id;\r\n  }\r\n}\r\n"}, "src/server/creators/UnknownCreator.ts": {"language": "typescript", "mutants": [{"id": "220", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/creators/UnknownCreator.ts(9,3): error TS2377: Constructors for derived classes must contain a 'super' call.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 16}, "start": {"column": 118, "line": 9}}}, {"id": "221", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 80, "line": 15}, "start": {"column": 68, "line": 15}}}, {"id": "222", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/creators/UnknownCreator.ts(23,6): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 31}, "start": {"column": 21, "line": 23}}}, {"id": "223", "mutatorName": "StringLiteral", "replacement": "``", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 59, "line": 29}, "start": {"column": 7, "line": 29}}}], "source": "import { LocalizedDate } from \"@eait-playerexp-cn/client-kernel\";\r\n\r\nexport default class Unknown<PERSON><PERSON> extends Error {\r\n  public readonly originEmail: string;\r\n  public readonly nucleusId: number;\r\n  public readonly userName: string;\r\n  public readonly dateOfBirth: string;\r\n\r\n  constructor(originEmail: string, nucleusId: number, defaultGamerTag: string, dateOfBirth: number, message: string) {\r\n    super(message);\r\n    this.originEmail = originEmail;\r\n    this.nucleusId = nucleusId;\r\n    this.message = message;\r\n    this.userName = defaultGamerTag;\r\n    this.dateOfBirth = LocalizedDate.format(new Date(dateOfBirth), \"YYYY-MM-DD\");\r\n  }\r\n\r\n  public static withIdentity(\r\n    originEmail: string,\r\n    nucleusId: number,\r\n    defaultGamerTag: string,\r\n    dateOfBirth: number\r\n  ): UnknownCreator {\r\n    return new UnknownCreator(\r\n      originEmail,\r\n      nucleusId,\r\n      defaultGamerTag,\r\n      dateOfBirth,\r\n      `Cannot find creator with Nucleus ID '${nucleusId}'`\r\n    );\r\n  }\r\n}\r\n"}, "src/server/healthCheck/HealthCheckController.ts": {"language": "typescript", "mutants": [{"id": "224", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/healthCheck/HealthCheckController.ts(12,3): error TS2377: Constructors for derived classes must contain a 'super' call.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["28"], "location": {"end": {"column": 4, "line": 14}, "start": {"column": 66, "line": 12}}}, {"id": "225", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: 200\n\nNumber of calls: 0\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\CreatorNetwork\\onboarding-micro-frontend\\strykerTmp\\sandbox7681143\\__tests__\\src\\server\\healthCheck\\HealthCheckController.spec.ts:19:24)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["28"], "coveredBy": ["28"], "location": {"end": {"column": 4, "line": 18}, "start": {"column": 86, "line": 16}}}, {"id": "226", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- Object {\n-   \"status\": \"UP\",\n- }\n+ Object {},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\CreatorNetwork\\onboarding-micro-frontend\\strykerTmp\\sandbox7681143\\__tests__\\src\\server\\healthCheck\\HealthCheckController.spec.ts:20:22)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["28"], "coveredBy": ["28"], "location": {"end": {"column": 43, "line": 17}, "start": {"column": 27, "line": 17}}}, {"id": "227", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  Object {\n-   \"status\": \"UP\",\n+   \"status\": \"\",\n  },\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\CreatorNetwork\\onboarding-micro-frontend\\strykerTmp\\sandbox7681143\\__tests__\\src\\server\\healthCheck\\HealthCheckController.spec.ts:20:22)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["28"], "coveredBy": ["28"], "location": {"end": {"column": 41, "line": 17}, "start": {"column": 37, "line": 17}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport {\r\n  Controller,\r\n  NextApiRequestWithSession,\r\n  RequestHandler,\r\n  type RequestHandlerOptions\r\n} from \"@eait-playerexp-cn/server-kernel\";\r\n\r\n@Service()\r\nclass HealthCheckController extends RequestHandler implements Controller {\r\n  constructor(@Inject(\"options\") options: RequestHandlerOptions) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(_req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    return this.json(res, { status: \"UP\" });\r\n  }\r\n}\r\n\r\nexport default HealthCheckController;\r\n"}, "src/server/legalDocuments/LegalDocumentsHttpClient.ts": {"language": "typescript", "mutants": [{"id": "228", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/legalDocuments/LegalDocumentsHttpClient.ts(13,46): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["22"], "location": {"end": {"column": 4, "line": 21}, "start": {"column": 76, "line": 13}}}, {"id": "229", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 27, "line": 15}, "start": {"column": 25, "line": 15}}}, {"id": "230", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "src/server/legalDocuments/LegalDocumentsHttpClient.ts(15,7): error TS2339: Property 'data' does not exist on type 'Boolean'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["22"], "location": {"end": {"column": 88, "line": 16}, "start": {"column": 9, "line": 16}}}, {"id": "231", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "src/server/legalDocuments/LegalDocumentsHttpClient.ts(15,7): error TS2339: Property 'data' does not exist on type 'Boolean'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["22"], "location": {"end": {"column": 88, "line": 16}, "start": {"column": 9, "line": 16}}}, {"id": "232", "mutatorName": "LogicalOperator", "replacement": "(await this.client.get(`/v1/accepted-terms-and-conditions/${creatorId}`)) && {}", "statusReason": "src/server/legalDocuments/LegalDocumentsHttpClient.ts(15,13): error TS2525: Initializer provides no value for this binding element and the binding element has no default value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["22"], "location": {"end": {"column": 88, "line": 16}, "start": {"column": 9, "line": 16}}}, {"id": "233", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"/v1/accepted-terms-and-conditions/a0YK0000004zHaoMAE\"\nReceived\n       1: \"\"\n       2: \"/v2/signed-contracts\", {\"query\": {\"creatorId\": \"a0YK0000004zHaoMAE\"}}\n\nNumber of calls: 2\n    at Object.toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/server/legalDocuments/LegalDocumentsHttpClient.spec.ts:25:24)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["22"], "coveredBy": ["22"], "location": {"end": {"column": 80, "line": 16}, "start": {"column": 32, "line": 16}}}, {"id": "234", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "src/server/legalDocuments/LegalDocumentsHttpClient.ts(20,5): error TS2322: Type '{ history: any; contracts: boolean; }' is not assignable to type 'SignedLegalDocuments'.\n  Types of property 'contracts' are incompatible.\n    Type 'boolean' is not assignable to type 'SignedContract[]'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["22"], "location": {"end": {"column": 107, "line": 18}, "start": {"column": 23, "line": 18}}}, {"id": "235", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "src/server/legalDocuments/LegalDocumentsHttpClient.ts(20,5): error TS2322: Type '{ history: any; contracts: boolean; }' is not assignable to type 'SignedLegalDocuments'.\n  Types of property 'contracts' are incompatible.\n    Type 'boolean' is not assignable to type 'SignedContract[]'.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["22"], "location": {"end": {"column": 107, "line": 18}, "start": {"column": 23, "line": 18}}}, {"id": "236", "mutatorName": "LogicalOperator", "replacement": "(await this.client.get(`/v2/signed-contracts`, {\n  query: {\n    creatorId\n  }\n})).data && []", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  -  1\n+ Received  + 10\n\n@@ -1,7 +1,16 @@\n  Object {\n-   \"contracts\": Array [],\n+   \"contracts\": Array [\n+     Object {\n+       \"documentUrl\": \"https://picsum.photos/seed/wD4iQzjCG/3594/3383\",\n+       \"label\": \"ue\",\n+       \"opportunityName\": [Function uuid],\n+       \"signedOnDate\": 1731260368000,\n+       \"uploaded\": true,\n+       \"uploadedByName\": \"<PERSON>\",\n+     },\n+   ],\n    \"history\": Array [\n      Object {\n        \"acceptedOnDate\": 1707586768000,\n        \"content\": \"C\",\n        \"creatorId\": \"470b58ae-eec0-4d56-91d9-3a100e308c8c\",\n    at Object.toEqual (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/server/legalDocuments/LegalDocumentsHttpClient.spec.ts:27:34)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["22"], "coveredBy": ["22"], "location": {"end": {"column": 107, "line": 18}, "start": {"column": 23, "line": 18}}}, {"id": "237", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"/v2/signed-contracts\", {\"query\": {\"creatorId\": \"a0YK0000004zHaoMAE\"}}\nReceived\n       1: \"/v1/accepted-terms-and-conditions/a0YK0000004zHaoMAE\"\n       2\n          \"\",\n          {\"query\": {\"creatorId\": \"a0YK0000004zHaoMAE\"}},\n\nNumber of calls: 2\n    at Object.toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/server/legalDocuments/LegalDocumentsHttpClient.spec.ts:26:24)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["22"], "coveredBy": ["22"], "location": {"end": {"column": 68, "line": 18}, "start": {"column": 46, "line": 18}}}, {"id": "238", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"/v2/signed-contracts\", {\"query\": {\"creatorId\": \"a0YK0000004zHaoMAE\"}}\nReceived\n       1: \"/v1/accepted-terms-and-conditions/a0YK0000004zHaoMAE\"\n       2\n          \"/v2/signed-contracts\",\n        - Object {\n        -   \"query\": Object {\n        -     \"creatorId\": \"a0YK0000004zHaoMAE\",\n        -   },\n        - }\n        + Object {},\n\nNumber of calls: 2\n    at Object.toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/server/legalDocuments/LegalDocumentsHttpClient.spec.ts:26:24)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["22"], "coveredBy": ["22"], "location": {"end": {"column": 94, "line": 18}, "start": {"column": 70, "line": 18}}}, {"id": "239", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"/v2/signed-contracts\", {\"query\": {\"creatorId\": \"a0YK0000004zHaoMAE\"}}\nReceived\n       1: \"/v1/accepted-terms-and-conditions/a0YK0000004zHaoMAE\"\n       2\n          \"/v2/signed-contracts\",\n          Object {\n        -   \"query\": Object {\n        -     \"creatorId\": \"a0YK0000004zHaoMAE\",\n        -   },\n        +   \"query\": Object {},\n          },\n\nNumber of calls: 2\n    at Object.toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/server/legalDocuments/LegalDocumentsHttpClient.spec.ts:26:24)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["22"], "coveredBy": ["22"], "location": {"end": {"column": 92, "line": 18}, "start": {"column": 79, "line": 18}}}, {"id": "240", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 107, "line": 18}, "start": {"column": 105, "line": 18}}}, {"id": "241", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/legalDocuments/LegalDocumentsHttpClient.ts(20,5): error TS2739: Type '{}' is missing the following properties from type 'SignedLegalDocuments': contracts, history\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["22"], "location": {"end": {"column": 50, "line": 20}, "start": {"column": 28, "line": 20}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport SignedLegalDocuments from \"./SignedLegalDocuments\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\n\r\n@Service()\r\nexport default class LegalDocumentsHttpClient {\r\n  constructor(@Inject(\"legalClient\") private readonly client: TraceableHttpClient) {}\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/legal/legal-api/docs/api.html#tag/Accepted-Terms-and-Conditions/paths/~1v1~1accepted-terms-and-conditions~1%7BcreatorId%7D/get Accepted Terms and conditions}\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/legal/legal-api/docs/api.html#tag/Signed-Contracts/operation/viewSignedContractsV2 Signed Contracts}\r\n   */\r\n  async allWithSignature(creatorId: string): Promise<SignedLegalDocuments> {\r\n    const {\r\n      data: { history = [] }\r\n    } = (await this.client.get(`/v1/accepted-terms-and-conditions/${creatorId}`)) || {};\r\n\r\n    const contracts = (await this.client.get(`/v2/signed-contracts`, { query: { creatorId } })).data || [];\r\n\r\n    return Promise.resolve({ history, contracts });\r\n  }\r\n}\r\n"}, "src/server/pactSafe/CachedTermsAndConditions.ts": {"language": "typescript", "mutants": [{"id": "242", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "Survived", "testsCompleted": 27, "static": true, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 53, "line": 14}, "start": {"column": 39, "line": 14}}}, {"id": "243", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/pactSafe/CachedTermsAndConditions.ts(22,90): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 31}, "start": {"column": 112, "line": 22}}}, {"id": "244", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 39, "line": 24}, "start": {"column": 9, "line": 24}}}, {"id": "245", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 39, "line": 24}, "start": {"column": 9, "line": 24}}}, {"id": "246", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 26}, "start": {"column": 41, "line": 24}}}, {"id": "247", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/pactSafe/CachedTermsAndConditions.ts(44,41): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 35}, "start": {"column": 74, "line": 33}}}, {"id": "248", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/pactSafe/CachedTermsAndConditions.ts(37,88): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 41}, "start": {"column": 102, "line": 37}}}, {"id": "249", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/pactSafe/CachedTermsAndConditions.ts(43,80): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 45}, "start": {"column": 87, "line": 43}}}, {"id": "250", "mutatorName": "StringLiteral", "replacement": "``", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 89, "line": 44}, "start": {"column": 12, "line": 44}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport TermsAndConditionsHttpClient from \"./TermsAndConditionsHttpClient\";\r\nimport SignedStatus from \"./SignedStatus\";\r\nimport SignerInformation from \"./SignerInformation\";\r\nimport { RedisCache } from \"@eait-playerexp-cn/server-kernel\";\r\nimport config from \"../../../config\";\r\n\r\nexport type SignerUrlWithTierOrProgram = {\r\n  contractUrl: string;\r\n};\r\n\r\n@Service()\r\nexport default class CachedTermsAndConditions {\r\n  private static readonly CACHE_KEY = \"signedStatus\";\r\n\r\n  constructor(\r\n    @Inject(\"termsAndConditions\")\r\n    private readonly termsAndConditions: TermsAndConditionsHttpClient,\r\n    private readonly cache: RedisCache\r\n  ) {}\r\n\r\n  async signedStatusWithProgram(creatorId: string, locale: string, programCode: string): Promise<SignedStatus> {\r\n    const cacheKey: string = this.getCacheKey(creatorId, locale, programCode);\r\n    if (await this.cache.has(cacheKey)) {\r\n      return (await this.cache.get(cacheKey)) as SignedStatus;\r\n    }\r\n    const status = await this.termsAndConditions.signedStatusWithProgram(creatorId);\r\n    await this.cache.set(cacheKey, status, config.TERMS_STATUS_CACHE_TTL);\r\n\r\n    return Promise.resolve(status);\r\n  }\r\n\r\n  signerUrl(signer: SignerInformation): Promise<Record<string, unknown>> {\r\n    return this.termsAndConditions.signerUrl(signer);\r\n  }\r\n\r\n  clearSignedStatusForProgram(creatorId: string, locale: string, programCode: string): Promise<void> {\r\n    const cacheKey: string = this.getCacheKey(creatorId, locale, programCode);\r\n\r\n    return this.cache.delete(cacheKey);\r\n  }\r\n\r\n  private getCacheKey(creatorId: string, locale: string, programCode: string): string {\r\n    return `${CachedTermsAndConditions.CACHE_KEY}_${creatorId}_${locale}_${programCode}`;\r\n  }\r\n}\r\n"}, "src/server/pactSafe/CheckTermsAndConditionsStatusAction.ts": {"language": "typescript", "mutants": [{"id": "251", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/pactSafe/CheckTermsAndConditionsStatusAction.ts(12,37): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 14}, "start": {"column": 59, "line": 12}}}], "source": "import SignedStatus from \"@src/server/pactSafe/SignedStatus\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport TermsAndConditions from \"@src/server/pactSafe/TermsAndConditionsHttpClient\";\r\n\r\n@Service()\r\nexport default class CheckTermsAndConditionsStatusAction {\r\n  constructor(\r\n    @Inject(\"cachedTermsAndConditions\")\r\n    private readonly termsAndConditions: TermsAndConditions\r\n  ) {}\r\n\r\n  async execute(creatorId: string): Promise<SignedStatus> {\r\n    return this.termsAndConditions.signedStatusWithProgram(creatorId);\r\n  }\r\n}\r\n"}, "src/server/pactSafe/ClearTermsAndConditionsStatusController.ts": {"language": "typescript", "mutants": [{"id": "252", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\CreatorNetwork\\onboarding-micro-frontend\\strykerTmp\\sandbox7681143\\__tests__\\src\\server\\termsAndConditions\\ClearTermsAndConditionsStatusController.spec.ts:35:54)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["17"], "coveredBy": ["17"], "location": {"end": {"column": 4, "line": 23}, "start": {"column": 85, "line": 16}}}], "source": "import { AuthenticatedRequestHand<PERSON> } from \"@eait-playerexp-cn/identity\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport CachedTermsAndConditions from \"../pactSafe/CachedTermsAndConditions\";\r\nimport { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\n@Service()\r\nclass ClearTermsAndConditionsStatusController extends AuthenticatedRequestHandler implements Controller {\r\n  constructor(\r\n    @Inject(\"options\") options: RequestHandlerOptions,\r\n    private readonly termsAndConditions: CachedTermsAndConditions\r\n  ) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const identity = this.identity(req);\r\n    const locale = this.locale(req);\r\n    const { program } = req.query as { program: string };\r\n    await this.termsAndConditions.clearSignedStatusForProgram(identity.id, locale, program);\r\n\r\n    this.json(res, {});\r\n  }\r\n}\r\n\r\nexport default ClearTermsAndConditionsStatusController;\r\n"}, "src/server/pactSafe/GenerateTermsAndConditionsUrlAction.ts": {"language": "typescript", "mutants": [{"id": "253", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/pactSafe/GenerateTermsAndConditionsUrlAction.ts(12,44): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 14}, "start": {"column": 77, "line": 12}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport TermsAndConditionsHttpClient from \"@src/server/pactSafe/TermsAndConditionsHttpClient\";\r\nimport SignerInformation from \"@src/server/pactSafe/SignerInformation\";\r\n\r\n@Service()\r\nexport default class GenerateTermsAndConditionsUrlAction {\r\n  constructor(\r\n    @Inject(\"cachedTermsAndConditions\")\r\n    private readonly termsAndConditions: TermsAndConditionsHttpClient\r\n  ) {}\r\n\r\n  async getUrl(signer: SignerInformation): Promise<Record<string, unknown>> {\r\n    return this.termsAndConditions.signerUrl(signer);\r\n  }\r\n}\r\n"}, "src/server/pactSafe/TermsAndConditionsHttpClient.ts": {"language": "typescript", "mutants": [{"id": "254", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/pactSafe/TermsAndConditionsHttpClient.ts(14,53): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["9"], "location": {"end": {"column": 4, "line": 17}, "start": {"column": 75, "line": 14}}}, {"id": "255", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"/v2/terms-and-conditions-status/********\"\nReceived: \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Onboarding\\onboarding-micro-frontend\\strykerTmp\\sandbox1964636\\__tests__\\src\\server\\termsAndConditions\\TermsAndConditionsHttpClient.spec.ts:20:24)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["9"], "coveredBy": ["9"], "location": {"end": {"column": 85, "line": 15}, "start": {"column": 39, "line": 15}}}, {"id": "256", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/pactSafe/TermsAndConditionsHttpClient.ts(24,47): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["10"], "location": {"end": {"column": 4, "line": 22}, "start": {"column": 80, "line": 19}}}, {"id": "257", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- \"/v3/terms-and-conditions/signing-url\",\n+ \"\",\n  {\"body\": {\"businessName\": \"test\", \"country\": \"UK\", \"creatorId\": \"234324\", \"email\": \"\", \"firstName\": \"\", \"lastName\": \"\", \"locale\": \"en-us\", \"program\": \"AFFILIATE\", \"screenName\": \"\"}},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/server/termsAndConditions/TermsAndConditionsHttpClient.spec.ts:50:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["10"], "coveredBy": ["10"], "location": {"end": {"column": 78, "line": 20}, "start": {"column": 40, "line": 20}}}, {"id": "258", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v3/terms-and-conditions/signing-url\",\n- Object {\n-   \"body\": Object {\n-     \"businessName\": \"test\",\n-     \"country\": \"UK\",\n-     \"creatorId\": \"234324\",\n-     \"email\": \"\",\n-     \"firstName\": \"\",\n-     \"lastName\": \"\",\n-     \"locale\": \"en-us\",\n-     \"program\": \"AFFILIATE\",\n-     \"screenName\": \"\",\n-   },\n- }\n+ Object {},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox5023598/__tests__/src/server/termsAndConditions/TermsAndConditionsHttpClient.spec.ts:50:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["10"], "coveredBy": ["10"], "location": {"end": {"column": 96, "line": 20}, "start": {"column": 80, "line": 20}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport SignerInformation from \"./SignerInformation\";\r\nimport SignedStatus from \"./SignedStatus\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\n\r\nexport type SigningUrl = {\r\n  contractUrl: string;\r\n};\r\n\r\n@Service()\r\nexport default class TermsAndConditionsHttpClient {\r\n  constructor(@Inject(\"legalClient\") private readonly client: TraceableHttpClient) {}\r\n\r\n  async signedStatusWithProgram(creatorId: string): Promise<SignedStatus> {\r\n    const res = await this.client.get(`/v2/terms-and-conditions-status/${creatorId}`);\r\n    return Promise.resolve(res.data as SignedStatus);\r\n  }\r\n\r\n  async signerUrl(signer: SignerInformation): Promise<Record<string, unknown>> {\r\n    const res = await this.client.post(\"/v3/terms-and-conditions/signing-url\", { body: signer });\r\n    return Promise.resolve(res.data);\r\n  }\r\n}\r\n"}, "src/server/pactSafe/ViewTermsAndConditionsSigningUrlWithProgramController.ts": {"language": "typescript", "mutants": [{"id": "259", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at Object.parse (/Users/<USER>/WebstormProjects/cn/onboarding-micro-frontend/strykerTmp/sandbox2239615/__tests__/src/server/termsAndConditions/ViewTermsAndConditionsSigningUrlWithProgramController.spec.ts:31:17)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["15"], "coveredBy": ["15"], "location": {"end": {"column": 4, "line": 23}, "start": {"column": 85, "line": 20}}}, {"id": "260", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/pactSafe/ViewTermsAndConditionsSigningUrlWithProgramController.ts(21,72): error TS2345: Argument of type '{}' is not assignable to parameter of type 'SignerInformation'.\n  Type '{}' is missing the following properties from type 'SignerInformation': businessName, creatorId, country, email, and 4 more.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["15"], "location": {"end": {"column": 87, "line": 21}, "start": {"column": 72, "line": 21}}}], "source": "import {\r\n  Controller,\r\n  NextApiRequestWithSession,\r\n  RequestHandler,\r\n  type RequestHandlerOptions\r\n} from \"@eait-playerexp-cn/server-kernel\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport TermsAndConditionsHttpClient from \"./TermsAndConditionsHttpClient\";\r\n\r\n@Service()\r\nclass ViewTermsAndConditionsSigningUrlWithProgramController extends RequestHandler implements Controller {\r\n  constructor(\r\n    @Inject(\"options\") options: RequestHandlerOptions,\r\n    private readonly termsAndConditions: TermsAndConditionsHttpClient\r\n  ) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const signingUrlResponse = await this.termsAndConditions.signerUrl({ ...req.body });\r\n    this.json(res, signingUrlResponse);\r\n  }\r\n}\r\n\r\nexport default ViewTermsAndConditionsSigningUrlWithProgramController;\r\n"}, "src/server/creatorCode/ValidatedCreatorCodesHttpClient.ts": {"language": "typescript", "mutants": [{"id": "89", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/creatorCode/ValidatedCreatorCodesHttpClient.ts(15,70): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "coveredBy": ["26"], "location": {"end": {"column": 4, "line": 18}, "start": {"column": 100, "line": 15}}}, {"id": "90", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- \"/v2/creator-code/TEST123\",\n+ \"\",\n  {\"query\": {\"creatorId\": \"creator-456\"}},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\CreatorNetwork\\onboarding-micro-frontend\\strykerTmp\\sandbox7155231\\__tests__\\src\\server\\creatorCode\\ValidatedCreatorCodesHttpClient.spec.ts:24:24)", "status": "Killed", "static": false, "testsCompleted": 1, "killedBy": ["26"], "coveredBy": ["26"], "location": {"end": {"column": 92, "line": 16}, "start": {"column": 59, "line": 16}}}, {"id": "91", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v2/creator-code/TEST123\",\n- Object {\n-   \"query\": Object {\n-     \"creatorId\": \"creator-456\",\n-   },\n- }\n+ Object {},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\CreatorNetwork\\onboarding-micro-frontend\\strykerTmp\\sandbox7155231\\__tests__\\src\\server\\creatorCode\\ValidatedCreatorCodesHttpClient.spec.ts:24:24)", "status": "Killed", "static": false, "testsCompleted": 1, "killedBy": ["26"], "coveredBy": ["26"], "location": {"end": {"column": 118, "line": 16}, "start": {"column": 94, "line": 16}}}, {"id": "92", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v2/creator-code/TEST123\",\n  Object {\n-   \"query\": Object {\n-     \"creatorId\": \"creator-456\",\n-   },\n+   \"query\": Object {},\n  },\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\CreatorNetwork\\onboarding-micro-frontend\\strykerTmp\\sandbox7155231\\__tests__\\src\\server\\creatorCode\\ValidatedCreatorCodesHttpClient.spec.ts:24:24)", "status": "Killed", "static": false, "testsCompleted": 1, "killedBy": ["26"], "coveredBy": ["26"], "location": {"end": {"column": 116, "line": 16}, "start": {"column": 103, "line": 16}}}], "source": "import { AxiosResponse } from \"axios\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\n\r\ntype ValidatedCreatorCode = {\r\n  creatorCode: string;\r\n  creatorId: string;\r\n  isValidCode: boolean;\r\n};\r\n\r\n@Service()\r\nclass ValidatedCreatorCodesHttpClient {\r\n  constructor(@Inject(\"operationsClient\") private readonly client: TraceableHttpClient) {}\r\n\r\n  async validateCreatorCode(creatorCode: string, creatorId: string): Promise<ValidatedCreatorCode> {\r\n    const response: AxiosResponse = await this.client.get(`/v2/creator-code/${creatorCode}`, { query: { creatorId } });\r\n    return response.data;\r\n  }\r\n}\r\n\r\nexport default ValidatedCreatorCodesHttpClient;\r\n"}, "src/server/creatorCode/ValidateCreatorCodeController.ts": {"language": "typescript", "mutants": [{"id": "88", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at Object.parse (C:\\Users\\<USER>\\CreatorNetwork\\onboarding-micro-frontend\\strykerTmp\\sandbox7155231\\__tests__\\src\\server\\creatorCode\\ValidateCreatorCodeController.spec.ts:39:17)", "status": "Killed", "static": false, "testsCompleted": 1, "killedBy": ["16"], "coveredBy": ["16"], "location": {"end": {"column": 4, "line": 23}, "start": {"column": 85, "line": 16}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ValidatedCreatorCodesHttpClient from \"./ValidatedCreatorCodesHttpClient\";\r\nimport { AuthenticatedRequestHandler } from \"@eait-playerexp-cn/identity\";\r\n\r\n@Service()\r\nclass ValidateCreatorCodeController extends AuthenticatedRequestHandler implements Controller {\r\n  constructor(\r\n    @Inject(\"options\") options: RequestHandlerOptions,\r\n    private readonly validatedCreatorCodesHttpClient: ValidatedCreatorCodesHttpClient\r\n  ) {\r\n    super(options);\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const creatorCode = req.query.code as string;\r\n    const creatorId = this.identity(req).id;\r\n\r\n    const creator = await this.validatedCreatorCodesHttpClient.validateCreatorCode(creatorCode, creatorId);\r\n\r\n    this.json(res, creator);\r\n  }\r\n}\r\n\r\nexport default ValidateCreatorCodeController;\r\n"}}, "schemaVersion": "1.0", "thresholds": {"high": 90, "low": 70, "break": 17.5}, "testFiles": {"__tests__/src/middleware/OAuthErrorHandler.spec.ts": {"tests": [{"id": "0", "name": "onOAuthError saves API Problem response with 'message' property in session and closes the current window", "location": {"start": {"column": 14, "line": 42}}}, {"id": "1", "name": "onOAuthError saves API Problem response with 'detail' property in session and closes the current window", "location": {"start": {"column": 14, "line": 42}}}, {"id": "2", "name": "onOAuthError saves `AxiosError` without response error message in session and closes the current window", "location": {"start": {"column": 4, "line": 59}}}, {"id": "3", "name": "onOAuthError saves `<PERSON><PERSON>r` message in session and closes the current window", "location": {"start": {"column": 4, "line": 80}}}], "source": "import \"reflect-metadata\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { ActivityFeed } from \"@eait-playerexp-cn/activity-feed\";\r\nimport { AxiosError } from \"axios\";\r\nimport { HttpRequest, HttpUrl } from \"@eait-playerexp-cn/http\";\r\nimport { LogLevel } from \"@eait-playerexp-cn/activity-logger\";\r\nimport { NextApiRequestWithMultipartFile } from \"@eait-playerexp-cn/server-kernel\";\r\nimport onOAuthError from \"@src/middleware/OAuthErrorHandler\";\r\nimport ApiContainer from \"../../../src/ApiContainer\";\r\n\r\njest.mock(\"../../../src/ApiContainer\");\r\n\r\ndescribe(\"onOAuthError\", () => {\r\n  const errorMessage = \"Cannot view terms and conditions url. Invalid input provided\";\r\n  const problem = {\r\n    title: \"Unprocessable Entity\",\r\n    status: 422,\r\n    type: \"https://tools.ietf.org/html/rfc4918#section-11.2\",\r\n    code: \"view-terms-and-conditions-url-invalid-input\",\r\n    errors: {\r\n      email: [\"must not be blank\"]\r\n    }\r\n  };\r\n  const config = { headers: {}, method: \"get\", baseURL: \"https://example.com\", url: \"/creators\" };\r\n  const req = {\r\n    method: \"GET\",\r\n    [Symbol.for(\"NextInternalRequestMeta\")]: { initURL: \"https://localhost:3000/\" },\r\n    session: { set: jest.fn(), save: jest.fn() }\r\n  } as unknown as NextApiRequestWithMultipartFile;\r\n  const res = { end: jest.fn() } as unknown as NextApiResponse;\r\n  const errorsWithApiProblem = [\r\n    [\"message\", { config, response: { data: { ...problem, message: errorMessage } } } as unknown as AxiosError],\r\n    [\"detail\", { config, response: { data: { ...problem, detail: errorMessage } } } as unknown as AxiosError]\r\n  ];\r\n  const feed = { add: jest.fn() } as unknown as ActivityFeed;\r\n\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n    (ApiContainer.get as jest.Mock).mockImplementation(() => feed);\r\n  });\r\n\r\n  test.each(errorsWithApiProblem)(\r\n    \"saves API Problem response with '%s' property in session and closes the current window\",\r\n    async (_property, error) => {\r\n      await onOAuthError(error as AxiosError, req, res);\r\n\r\n      expect(feed.add).toHaveBeenCalledTimes(0); // Since Axios errors have already been logged\r\n      expect(req.session.error).toEqual({\r\n        code: \"view-terms-and-conditions-url-invalid-input\",\r\n        message: errorMessage\r\n      });\r\n      expect(req.session.save).toHaveBeenCalledTimes(1);\r\n      expect(req.session.save).toHaveBeenCalledWith();\r\n      expect(res.end).toHaveBeenCalledTimes(1);\r\n      expect(res.end).toHaveBeenCalledWith(\"<script>window.close();</script>\");\r\n    }\r\n  );\r\n\r\n  it(\"saves `AxiosError` without response error message in session and closes the current window\", async () => {\r\n    const error = { config, message: \"Connection timed out\" } as AxiosError;\r\n    await onOAuthError(error, req, res);\r\n\r\n    expect(feed.add).toHaveBeenCalledTimes(1);\r\n    expect(feed.add).toHaveBeenCalledWith({\r\n      context: {\r\n        exception: error,\r\n        identifier: \"application-error\",\r\n        request: new HttpRequest(\"GET\", new HttpUrl(new URL(\"https://localhost:3000/\")))\r\n      },\r\n      level: LogLevel.ERROR,\r\n      message: \"Connection timed out\"\r\n    });\r\n    expect(req.session.error).toEqual(\"Connection timed out\");\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n    expect(req.session.save).toHaveBeenCalledWith();\r\n    expect(res.end).toHaveBeenCalledTimes(1);\r\n    expect(res.end).toHaveBeenCalledWith(\"<script>window.close();</script>\");\r\n  });\r\n\r\n  it(\"saves `Error` message in session and closes the current window\", async () => {\r\n    const error = new Error();\r\n    await onOAuthError(error, req, res);\r\n\r\n    expect(feed.add).toHaveBeenCalledTimes(1);\r\n    expect(feed.add).toHaveBeenCalledWith({\r\n      context: {\r\n        exception: error,\r\n        identifier: \"application-error\",\r\n        request: new HttpRequest(\"GET\", new HttpUrl(new URL(\"https://localhost:3000/\")))\r\n      },\r\n      level: LogLevel.ERROR,\r\n      message: \"No message provided\"\r\n    });\r\n    expect(req.session.error).toEqual(\"No message provided\");\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n    expect(req.session.save).toHaveBeenCalledWith();\r\n    expect(res.end).toHaveBeenCalledTimes(1);\r\n    expect(res.end).toHaveBeenCalledWith(\"<script>window.close();</script>\");\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ViewConnectedAccountsController.spec.ts": {"tests": [{"id": "4", "name": "ViewConnectedAccountsController fetch connected accounts for the interested creators", "location": {"start": {"column": 4, "line": 30}}}, {"id": "5", "name": "ViewConnectedAccountsController finds all connected accounts for an interested creator with expiration status", "location": {"start": {"column": 4, "line": 53}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiRequestWithSession, RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { NextApiResponse } from \"next\";\r\nimport ViewConnectedAccountsController from \"@src/server/connecteAccounts/ViewConnectedAccountsController\";\r\nimport config from \"../../../../config\";\r\nimport ConnectedAccountsHttpClient from \"@src/server/connecteAccounts/ConnectedAccountsHttpClient\";\r\nimport { Random } from \"@eait-playerexp-cn/onboarding-ui\";\r\n\r\njest.mock(\"../../../../config\");\r\n\r\ndescribe(\"ViewConnectedAccountsController\", () => {\r\n  let controller: ViewConnectedAccountsController;\r\n  const options = {} as RequestHandlerOptions;\r\n  const session = { save: jest.fn() };\r\n  const account = {\r\n    name: Random.firstName(),\r\n    disconnected: false,\r\n    username: Random.userName(),\r\n    id: Random.uuid(),\r\n    type: \"YOUTUBE\",\r\n    uri: Random.url(),\r\n    thumbnail: Random.imageUrl(),\r\n    isExpired: true,\r\n    accountId: Random.uuid()\r\n  };\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"fetch connected accounts for the interested creators\", async () => {\r\n    config.INTERESTED_CREATOR_REAPPLY_PERIOD = false;\r\n    const nucleusId = \"abc124444cfhfkflddfgfh\";\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: \"/api/connected-accounts\",\r\n      query: { nucleusId },\r\n      session\r\n    });\r\n    const connectedAccounts = [account];\r\n    const accounts = { getAllConnectedAccounts: jest.fn().mockResolvedValue(connectedAccounts) };\r\n    controller = new ViewConnectedAccountsController(options, accounts as unknown as ConnectedAccountsHttpClient);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(connectedAccounts);\r\n    expect(accounts.getAllConnectedAccounts).toHaveBeenCalledTimes(1);\r\n    expect(accounts.getAllConnectedAccounts).toHaveBeenCalledWith(nucleusId);\r\n    expect(req.session.nucleusId).toEqual(nucleusId);\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n\r\n  it(\"finds all connected accounts for an interested creator with expiration status\", async () => {\r\n    config.INTERESTED_CREATOR_REAPPLY_PERIOD = true;\r\n    const nucleusId = \"abc124444cfhfkflddfgfh\";\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: \"/api/v2/connected-accounts\",\r\n      query: { nucleusId },\r\n      session\r\n    });\r\n    const connectedAccounts = [account];\r\n    const accounts = { getAllConnectedAccountsWithExpirationStatus: jest.fn().mockResolvedValue(connectedAccounts) };\r\n    controller = new ViewConnectedAccountsController(options, accounts as unknown as ConnectedAccountsHttpClient);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(connectedAccounts);\r\n    expect(accounts.getAllConnectedAccountsWithExpirationStatus).toHaveBeenCalledTimes(1);\r\n    expect(accounts.getAllConnectedAccountsWithExpirationStatus).toHaveBeenCalledWith(nucleusId);\r\n    expect(req.session.nucleusId).toEqual(nucleusId);\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts": {"tests": [{"id": "6", "name": "ConnectedAccountsHttpClient finds all connected accounts", "location": {"start": {"column": 4, "line": 7}}}, {"id": "7", "name": "ConnectedAccountsHttpClient connects a Facebook page", "location": {"start": {"column": 4, "line": 31}}}, {"id": "8", "name": "ConnectedAccountsHttpClient removes a connected account for an interested creator", "location": {"start": {"column": 4, "line": 49}}}], "source": "import \"reflect-metadata\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport ConnectedAccountsHttpClient from \"@src/server/connecteAccounts/ConnectedAccountsHttpClient\";\r\nimport { Random } from \"@eait-playerexp-cn/onboarding-ui\";\r\n\r\ndescribe(\"ConnectedAccountsHttpClient\", () => {\r\n  it(\"finds all connected accounts\", async () => {\r\n    const account = {\r\n      name: Random.firstName(),\r\n      disconnected: false,\r\n      username: Random.userName(),\r\n      id: Random.uuid(),\r\n      type: \"YOUTUBE\",\r\n      uri: Random.url(),\r\n      thumbnail: Random.imageUrl(),\r\n      isExpired: true,\r\n      accountId: Random.uuid()\r\n    };\r\n    const connectedAccounts = [account];\r\n    const nucleusId = \"abc124476458fhgfghl\";\r\n    const client = { get: jest.fn().mockReturnValue({ data: connectedAccounts }) };\r\n    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const accounts = await connectedAccountsHttpClient.getAllConnectedAccounts(nucleusId);\r\n\r\n    expect(accounts).toEqual(connectedAccounts);\r\n    expect(client.get).toHaveBeenCalledTimes(1);\r\n    expect(client.get).toHaveBeenCalledWith(`/v1/connected-accounts/${nucleusId}`);\r\n  });\r\n\r\n  it(\"connects a Facebook page\", async () => {\r\n    const nucleusId = \"abc124476458fhgfghl\";\r\n    const credentials = {\r\n      accessToken: Random.string(),\r\n      creatorId: null,\r\n      nucleusId,\r\n      pageAccessToken: Random.string(),\r\n      pageId: \"a057717c-01f2-49de-9422-289b06649809\"\r\n    };\r\n    const client = { post: jest.fn().mockReturnValue({ data: null }) };\r\n    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    await connectedAccountsHttpClient.connectFacebookPage(credentials);\r\n\r\n    expect(client.post).toHaveBeenCalledTimes(1);\r\n    expect(client.post).toHaveBeenCalledWith(\"/v1/facebook-accounts\", { body: credentials });\r\n  });\r\n\r\n  it(\"removes a connected account for an interested creator\", async () => {\r\n    const accountId = \"a1LDF00000K4z0i2AB\";\r\n    const type = \"INTERESTED_CREATOR\";\r\n    const client = { delete: jest.fn().mockReturnValue({ data: null }) };\r\n    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    await connectedAccountsHttpClient.removeConnectedAccount(accountId, type);\r\n\r\n    expect(client.delete).toHaveBeenCalledTimes(1);\r\n    expect(client.delete).toHaveBeenCalledWith(`/v1/connected-accounts/${accountId}`, { query: { type } });\r\n  });\r\n});\r\n"}, "__tests__/src/server/termsAndConditions/TermsAndConditionsHttpClient.spec.ts": {"tests": [{"id": "9", "name": "TermsAndConditionsHttpClient calls a signed status with program for terms and conditions", "location": {"start": {"column": 4, "line": 10}}}, {"id": "10", "name": "TermsAndConditionsHttpClient calls a signing URL for pact safe", "location": {"start": {"column": 4, "line": 22}}}], "source": "import \"reflect-metadata\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport TermsAndConditionsHttpClient from \"@src/server/pactSafe/TermsAndConditionsHttpClient\";\r\n\r\njest.mock(\"uuid\");\r\n\r\ndescribe(\"TermsAndConditionsHttpClient\", () => {\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"calls a signed status with program for terms and conditions\", async () => {\r\n    const creatorId = \"********\";\r\n    const client = { get: jest.fn().mockReturnValue({ data: { upToDate: false } }) };\r\n    const signedStatusResponse = new TermsAndConditionsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const application = await signedStatusResponse.signedStatusWithProgram(creatorId);\r\n\r\n    expect(application).toEqual({ upToDate: false });\r\n    expect(client.get).toHaveBeenCalledTimes(1);\r\n    expect(client.get).toHaveBeenCalledWith(`/v2/terms-and-conditions-status/${creatorId}`);\r\n  });\r\n\r\n  it(\"calls a signing URL for pact safe\", async () => {\r\n    const signerInformation = {\r\n      businessName: \"test\",\r\n      creatorId: \"234324\",\r\n      country: \"UK\",\r\n      email: \"\",\r\n      firstName: \"\",\r\n      lastName: \"\",\r\n      screenName: \"\",\r\n      locale: \"en-us\",\r\n      program: \"AFFILIATE\"\r\n    };\r\n    const signerUrlWithProgram = {\r\n      contractUrl:\r\n        'https://app.pactsafe.com/sign?r=60d22c838ea821120cee5998&s=60c2f746704ffb0e40d92edc&signature=leSvpmgIQMLPi7Ua2Pl4Z0i5AkTRFzO6km3Q2FLMzeSkx9ZIRA7Bb59PCiFsv3vQ4oQTd-4~0kNsOMykCtwi5Vp9Qe9aqmaC~UNTPuwiCnhYSIdkfG88YkWUF1xdFDOZUWkcdPG-~sVLn8MAj8p0vtlYczeydgMsdVHWcRkjBYa3Z~BoqMs1bkt0m7tFovXsh2Aenos3CKaDB118ipQ1CbGmwcdbbWcVptIaqo0ES85cKV-5Cx~vEqnnO18uy6IdAeOsFsbKEQBh2kYFZymGdCgfv65fH6vof9hRaE--TCUnYR~QZm01uO1Cpb02fI320wg1eBWjNWivJucA7-lyrg__\",\"token\":\"aL0BIJp~PBpgsMm4mte4aG-mdtwVjkxOV4STqA0lxy4_'\r\n    };\r\n    const client = { post: jest.fn().mockReturnValue({ data: signerUrlWithProgram }) };\r\n    const signerUrlWithProgramForPactSafe = new TermsAndConditionsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const application = await signerUrlWithProgramForPactSafe.signerUrl(signerInformation);\r\n\r\n    expect(application).toEqual(signerUrlWithProgram);\r\n    expect(client.post).toHaveBeenCalledTimes(1);\r\n    expect(client.post).toHaveBeenCalledWith(\"/v3/terms-and-conditions/signing-url\", { body: signerInformation });\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/RemoveConnectedAccountController.spec.ts": {"tests": [{"id": "11", "name": "RemoveConnectedAccountController removes interested creator account", "location": {"start": {"column": 4, "line": 15}}}, {"id": "12", "name": "RemoveConnectedAccountController removes creator account", "location": {"start": {"column": 4, "line": 33}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession, RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport RemoveConnectedAccountController from \"@src/server/connecteAccounts/RemoveConnectedAccountController\";\r\nimport ConnectedAccountsHttpClient from \"@src/server/connecteAccounts/ConnectedAccountsHttpClient\";\r\n\r\ndescribe(\"RemoveConnectedAccountController\", () => {\r\n  let controller: RemoveConnectedAccountController;\r\n  const options = {} as RequestHandlerOptions;\r\n  const session = { save: jest.fn() };\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"removes interested creator account\", async () => {\r\n    const nucleusId = \"************\";\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"DELETE\",\r\n      url: \"api/remove-account\",\r\n      query: { id: \"a1LDF00000K4z0i2AB\" },\r\n      session: { ...session, nucleusId }\r\n    });\r\n    const accounts = { removeConnectedAccount: jest.fn() } as unknown as ConnectedAccountsHttpClient;\r\n    controller = new RemoveConnectedAccountController(options, accounts);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(accounts.removeConnectedAccount).toHaveBeenCalledTimes(1);\r\n    expect(accounts.removeConnectedAccount).toHaveBeenCalledWith(req.query.id, \"INTERESTED_CREATOR\");\r\n  });\r\n\r\n  it(\"removes creator account\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"DELETE\",\r\n      url: \"api/remove-account\",\r\n      query: { id: \"a1LDF00000K4z0i2AB\" },\r\n      session: { ...session, user: {} }\r\n    });\r\n    const accounts = { removeConnectedAccount: jest.fn() } as unknown as ConnectedAccountsHttpClient;\r\n    controller = new RemoveConnectedAccountController(options, accounts);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(accounts.removeConnectedAccount).toHaveBeenCalledTimes(1);\r\n    expect(accounts.removeConnectedAccount).toHaveBeenCalledWith(req.query.id, \"CREATOR\");\r\n  });\r\n});\r\n"}, "__tests__/src/shared/tokens/CachedAccessTokenProvider.spec.ts": {"tests": [{"id": "13", "name": "CachedAccessTokenProvider should return access token from cache if available", "location": {"start": {"column": 4, "line": 20}}}, {"id": "14", "name": "CachedAccessTokenProvider should fetch a new access token and cache it if not available", "location": {"start": {"column": 4, "line": 33}}}], "source": "import { AccessToken, OAuthTokenProvider } from \"@eait-playerexp-cn/http-client\";\r\nimport { RedisCache } from \"@eait-playerexp-cn/server-kernel\";\r\nimport CachedAccessTokenProvider from \"@src/shared/tokens/CachedAccessTokenProvider\";\r\n\r\ndescribe(\"CachedAccessTokenProvider\", () => {\r\n  let tokenProvider: OAuthTokenProvider;\r\n  let cache: RedisCache;\r\n  let cachedAccessTokenProvider: CachedAccessTokenProvider;\r\n\r\n  beforeEach(() => {\r\n    tokenProvider = { accessToken: jest.fn() } as unknown as OAuthTokenProvider;\r\n    cache = { has: jest.fn(), get: jest.fn(), set: jest.fn() } as unknown as RedisCache;\r\n    cachedAccessTokenProvider = new CachedAccessTokenProvider(tokenProvider, cache);\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  it(\"should return access token from cache if available\", async () => {\r\n    const mockAccessToken = { token: \"cached-token\", expiresIn: 3600 } as AccessToken;\r\n    (cache.has as jest.Mock).mockResolvedValue(true);\r\n    (cache.get as jest.Mock).mockResolvedValue(mockAccessToken);\r\n\r\n    const accessToken = await cachedAccessTokenProvider.accessToken();\r\n\r\n    expect(accessToken).toEqual(mockAccessToken);\r\n    expect(cache.has).toHaveBeenCalledWith(\"accessToken\");\r\n    expect(cache.get).toHaveBeenCalledWith(\"accessToken\");\r\n    expect(tokenProvider.accessToken).not.toHaveBeenCalled(); // Ensure provider was not called\r\n  });\r\n\r\n  it(\"should fetch a new access token and cache it if not available\", async () => {\r\n    const mockAccessToken = { token: \"new-token\", expiresIn: 3600 } as AccessToken;\r\n    (cache.has as jest.Mock).mockResolvedValue(false);\r\n    (tokenProvider.accessToken as jest.Mock).mockResolvedValue(mockAccessToken);\r\n\r\n    const accessToken = await cachedAccessTokenProvider.accessToken();\r\n\r\n    expect(accessToken).toEqual(mockAccessToken);\r\n    expect(cache.has).toHaveBeenCalledWith(\"accessToken\");\r\n    expect(tokenProvider.accessToken).toHaveBeenCalled(); // Ensure provider was called\r\n    expect(cache.set).toHaveBeenCalledWith(\"accessToken\", mockAccessToken, mockAccessToken.expiresIn - 5);\r\n  });\r\n});\r\n"}, "__tests__/src/server/termsAndConditions/ViewTermsAndConditionsSigningUrlWithProgramController.spec.ts": {"tests": [{"id": "15", "name": "ViewTermsAndConditionsSigningUrlWithProgramController get signing url for pact safe", "location": {"start": {"column": 4, "line": 14}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiRequestWithSession, RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { NextApiResponse } from \"next\";\r\nimport ViewTermsAndConditionsSigningUrlWithProgramController from \"@src/server/pactSafe/ViewTermsAndConditionsSigningUrlWithProgramController\";\r\nimport TermsAndConditionsHttpClient from \"@src/server/pactSafe/TermsAndConditionsHttpClient\";\r\n\r\ndescribe(\"ViewTermsAndConditionsSigningUrlWithProgramController\", () => {\r\n  let controller: ViewTermsAndConditionsSigningUrlWithProgramController;\r\n  const options = {} as RequestHandlerOptions;\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"get signing url for pact safe\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: `v2/terms-and-conditions/signing-url`\r\n    });\r\n    const signingUrl = {\r\n      contractUrl:\r\n        'https://app.pactsafe.com/sign?r=60d22c838ea821120cee5998&s=60c2f746704ffb0e40d92edc&signature=leSvpmgIQMLPi7Ua2Pl4Z0i5AkTRFzO6km3Q2FLMzeSkx9ZIRA7Bb59PCiFsv3vQ4oQTd-4~0kNsOMykCtwi5Vp9Qe9aqmaC~UNTPuwiCnhYSIdkfG88YkWUF1xdFDOZUWkcdPG-~sVLn8MAj8p0vtlYczeydgMsdVHWcRkjBYa3Z~BoqMs1bkt0m7tFovXsh2Aenos3CKaDB118ipQ1CbGmwcdbbWcVptIaqo0ES85cKV-5Cx~vEqnnO18uy6IdAeOsFsbKEQBh2kYFZymGdCgfv65fH6vof9hRaE--TCUnYR~QZm01uO1Cpb02fI320wg1eBWjNWivJucA7-lyrg__\",\"token\":\"aL0BIJp~PBpgsMm4mte4aG-mdtwVjkxOV4STqA0lxy4_'\r\n    };\r\n    controller = new ViewTermsAndConditionsSigningUrlWithProgramController(options, {\r\n      signerUrl: jest.fn().mockImplementation(() => signingUrl)\r\n    } as unknown as TermsAndConditionsHttpClient);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(signingUrl);\r\n  });\r\n});\r\n"}, "__tests__/src/server/creatorCode/ValidateCreatorCodeController.spec.ts": {"tests": [{"id": "16", "name": "ValidateCreatorCodeController validates a creator code and returns the result", "location": {"start": {"column": 4, "line": 19}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession, RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ValidateCreatorCodeController from \"@src/server/creatorCode/ValidateCreatorCodeController\";\r\nimport ValidatedCreatorCodesHttpClient from \"@src/server/creatorCode/ValidatedCreatorCodesHttpClient\";\r\nimport { aStoredIdentity } from \"@eait-playerexp-cn/identity-test-fixtures\";\r\nimport { Identity } from \"@eait-playerexp-cn/identity-types\";\r\n\r\ndescribe(\"ValidateCreatorCodeController\", () => {\r\n  let controller: ValidateCreatorCodeController;\r\n  const options = {} as RequestHandlerOptions;\r\n  const identity = Identity.fromStored(aStoredIdentity());\r\n\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  it(\"validates a creator code and returns the result\", async () => {\r\n    const creatorCode = \"TEST123\";\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: `/api/creator-codes/${creatorCode}`,\r\n      query: { code: creatorCode }\r\n    });\r\n    req.session = { identity, save: jest.fn() };\r\n    const validationResult = {\r\n      creatorCode: \"TEST123\",\r\n      creatorId: identity.id,\r\n      isValidCode: true\r\n    };\r\n    const client = { validateCreatorCode: jest.fn().mockResolvedValue(validationResult) };\r\n    controller = new ValidateCreatorCodeController(options, client as unknown as ValidatedCreatorCodesHttpClient);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(validationResult);\r\n    expect(client.validateCreatorCode).toHaveBeenCalledTimes(1);\r\n    expect(client.validateCreatorCode).toHaveBeenCalledWith(creatorCode, identity.id);\r\n  });\r\n});\r\n"}, "__tests__/src/server/termsAndConditions/ClearTermsAndConditionsStatusController.spec.ts": {"tests": [{"id": "17", "name": "ClearTermsAndConditionsStatusController clears signed status detail from cache", "location": {"start": {"column": 4, "line": 17}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession, RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport CachedTermsAndConditions from \"@src/server/pactSafe/CachedTermsAndConditions\";\r\nimport ClearTermsAndConditionsStatusController from \"@src/server/pactSafe/ClearTermsAndConditionsStatusController\";\r\nimport { aStoredIdentity } from \"@eait-playerexp-cn/identity-test-fixtures\";\r\nimport { Identity } from \"@eait-playerexp-cn/identity-types\";\r\n\r\njest.mock(\"../../../../config\");\r\n\r\ndescribe(\"ClearTermsAndConditionsStatusController\", () => {\r\n  let controller: ClearTermsAndConditionsStatusController;\r\n  const options = {} as RequestHandlerOptions;\r\n  const session = { save: jest.fn() };\r\n\r\n  it(\"clears signed status detail from cache\", async () => {\r\n    const identity = Identity.fromStored(aStoredIdentity());\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"DELETE\",\r\n      url: `/v2/terms-and-conditions/terms-and-conditions-status`,\r\n      query: { program: \"affiliate\" }\r\n    });\r\n    req.session = { identity, save: jest.fn() };\r\n    const signedStatus = { clearSignedStatusForProgram: jest.fn() };\r\n    controller = new ClearTermsAndConditionsStatusController(\r\n      options,\r\n      signedStatus as unknown as CachedTermsAndConditions\r\n    );\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(signedStatus.clearSignedStatusForProgram).toHaveBeenCalledTimes(1);\r\n    expect(signedStatus.clearSignedStatusForProgram).toHaveBeenCalledWith(identity.id, \"en-us\", \"affiliate\");\r\n  });\r\n});\r\n"}, "__tests__/src/shared/logging/SentryRecorder.spec.ts": {"tests": [{"id": "18", "name": "SentryRecorder records error activities", "location": {"start": {"column": 4, "line": 16}}}, {"id": "19", "name": "SentryRecorder ignores info activities", "location": {"start": {"column": 14, "line": 36}}}, {"id": "20", "name": "SentryRecorder ignores warn activities", "location": {"start": {"column": 14, "line": 36}}}, {"id": "21", "name": "SentryRecorder ignores debug activities", "location": {"start": {"column": 14, "line": 36}}}], "source": "import * as Sentry from \"@sentry/nextjs\";\r\nimport { Activity } from \"@eait-playerexp-cn/activity-feed\";\r\nimport SentryRecorder from \"@src/shared/logging/SentryRecorder\";\r\n\r\njest.mock(\"@sentry/nextjs\");\r\n\r\ndescribe(\"SentryRecorder\", () => {\r\n  const nonErrorActivities = [\r\n    [\"info\", Activity.info(\"info-identifier\", \"Informational message\", {})],\r\n    [\"warn\", Activity.warn(\"warning-identifier\", \"Warning message\", {})],\r\n    [\"debug\", Activity.debug(\"debug-identifier\", \"Debugging message\", {})]\r\n  ];\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"records error activities\", () => {\r\n    const exception = new Error(\"undefined is not a function\");\r\n    const activity = Activity.error(\"activity-identifier\", \"Something wrong occurred\", {\r\n      exception,\r\n      key: \"value\"\r\n    });\r\n    const recorder = new SentryRecorder();\r\n\r\n    recorder.record(activity);\r\n\r\n    expect(Sentry.captureException).toHaveBeenCalledTimes(1);\r\n    expect(Sentry.captureException).toHaveBeenCalledWith(exception, {\r\n      extra: {\r\n        exception,\r\n        identifier: \"activity-identifier\",\r\n        key: \"value\"\r\n      }\r\n    });\r\n  });\r\n\r\n  test.each(nonErrorActivities)(\"ignores %s activities\", (_level, activity: Activity) => {\r\n    const recorder = new SentryRecorder();\r\n\r\n    recorder.record(activity);\r\n\r\n    expect(Sentry.captureException).toHaveBeenCalledTimes(0);\r\n  });\r\n});\r\n"}, "__tests__/src/server/legalDocuments/LegalDocumentsHttpClient.spec.ts": {"tests": [{"id": "22", "name": "LegalDocumentsHttpClient gets signed contract legal documents", "location": {"start": {"column": 4, "line": 7}}}], "source": "import \"reflect-metadata\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport LegalDocumentsHttpClient from \"@src/server/legalDocuments/LegalDocumentsHttpClient\";\r\nimport { someSignedLegalDocuments } from \"__tests__/factories/legalDocuments/SignedLegalDocuments\";\r\n\r\ndescribe(\"LegalDocumentsHttpClient\", () => {\r\n  it(\"gets signed contract legal documents\", async () => {\r\n    const id = \"a0YK0000004zHaoMAE\";\r\n    const signedLegalDocuments = someSignedLegalDocuments();\r\n    const client = {\r\n      get: jest.fn().mockImplementation((url) => {\r\n        if (url === `/v1/accepted-terms-and-conditions/${id}`) {\r\n          return { data: { history: signedLegalDocuments.history } };\r\n        } else {\r\n          return { data: signedLegalDocuments.contracts };\r\n        }\r\n      })\r\n    };\r\n    const legalDocuments = new LegalDocumentsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const legalDocumentsResponse = await legalDocuments.allWithSignature(id);\r\n\r\n    expect(client.get).toHaveBeenCalledTimes(2);\r\n    expect(client.get).toHaveBeenCalledWith(`/v1/accepted-terms-and-conditions/${id}`);\r\n    expect(client.get).toHaveBeenCalledWith(`/v2/signed-contracts`, { query: { creatorId: id } });\r\n    expect(signedLegalDocuments).toEqual(legalDocumentsResponse);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ViewRecentErrorsController.spec.ts": {"tests": [{"id": "23", "name": "ViewRecentErrorsController shows the error stored in session", "location": {"start": {"column": 4, "line": 13}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiRequestWithSession, RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { NextApiResponse } from \"next\";\r\nimport ViewRecentErrorsController from \"@src/server/connecteAccounts/ViewRecentErrorsController\";\r\n\r\ndescribe(\"ViewRecentErrorsController\", () => {\r\n  let controller: ViewRecentErrorsController;\r\n  const options = {} as RequestHandlerOptions;\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"shows the error stored in session\", async () => {\r\n    const error = { code: \"view-facebook-pages-invalid-input\", message: \"Please try with appropriate permissions\" };\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: \"/api/errors\",\r\n      session: { error }\r\n    });\r\n    controller = new ViewRecentErrorsController(options);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(error);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ClearSessionsKeyController.spec.ts": {"tests": [{"id": "24", "name": "ClearSessionsKeyController removes 'error' from the session", "location": {"start": {"column": 4, "line": 14}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession, RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ClearSessionsKeyController from \"@src/server/connecteAccounts/ClearSessionsKeyController\";\r\n\r\ndescribe(\"ClearSessionsKeyController\", () => {\r\n  let controller: ClearSessionsKeyController;\r\n  const options = {} as RequestHandlerOptions;\r\n  const session = { save: jest.fn() };\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"removes 'error' from the session\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"DELETE\",\r\n      url: \"/api/errors\",\r\n      query: { key: \"error\" },\r\n      session\r\n    });\r\n    controller = new ClearSessionsKeyController(options);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.error).toBeUndefined();\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ClearAccountTypeController.spec.ts": {"tests": [{"id": "25", "name": "ClearAccountTypeController removes account type from the session", "location": {"start": {"column": 4, "line": 14}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession, RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ClearAccountTypeController from \"@src/server/connecteAccounts/ClearAccountTypeController\";\r\n\r\ndescribe(\"ClearAccountTypeController\", () => {\r\n  let controller: ClearAccountTypeController;\r\n  const options = {} as RequestHandlerOptions;\r\n  const session = { save: jest.fn() };\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"removes account type from the session\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/account-types\",\r\n      session\r\n    });\r\n    controller = new ClearAccountTypeController(options);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.accountType).toBeUndefined();\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n});\r\n"}, "__tests__/src/server/creatorCode/ValidatedCreatorCodesHttpClient.spec.ts": {"tests": [{"id": "26", "name": "ValidatedCreatorCodesHttpClient validates a creator code", "location": {"start": {"column": 4, "line": 9}}}], "source": "import ValidatedCreatorCodesHttpClient from \"../../../../src/server/creatorCode/ValidatedCreatorCodesHttpClient\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\n\r\ndescribe(\"ValidatedCreatorCodesHttpClient\", () => {\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  it(\"validates a creator code\", async () => {\r\n    const response = {\r\n      data: {\r\n        creatorCode: \"TEST123\",\r\n        creatorId: \"creator-456\",\r\n        isValidCode: true\r\n      }\r\n    };\r\n\r\n    const client = { get: jest.fn().mockReturnValue(response) } as unknown as TraceableHttpClient;\r\n    const registrationCodes = new ValidatedCreatorCodesHttpClient(client);\r\n\r\n    const result = await registrationCodes.validateCreatorCode(\"TEST123\", \"creator-456\");\r\n\r\n    expect(client.get).toHaveBeenCalledWith(\"/v2/creator-code/TEST123\", {\r\n      query: { creatorId: \"creator-456\" }\r\n    });\r\n    expect(result.isValidCode).toBe(true);\r\n    expect(result.creatorCode).toBe(\"TEST123\");\r\n    expect(result.creatorId).toBe(\"creator-456\");\r\n  });\r\n});\r\n"}, "__tests__/src/actions/Creators/Avatar/AvatarAction.spec.ts": {"tests": [{"id": "27", "name": "UploadCreatorAvatarAction fails to upload avatar if no files uploaded", "location": {"start": {"column": 4, "line": 16}}}], "source": "import AvatarInput from \"@src/server/Avatar/AvatarInput\";\r\nimport AvatarUpload from \"@src/server/Avatar/AvatarUpload\";\r\nimport UploadCreatorAvatarAction from \"@src/server/Avatar/UploadCreatorAvatarAction\";\r\nimport CreatorsHttpClient from \"@src/server/creators/CreatorsHttpClient\";\r\nimport \"reflect-metadata\";\r\n\r\ndescribe(\"UploadCreatorAvatarAction\", () => {\r\n  let creators: CreatorsHttpClient = null;\r\n  let action: UploadCreatorAvatarAction = null;\r\n\r\n  beforeEach(() => {\r\n    creators = {} as CreatorsHttpClient;\r\n    action = new UploadCreatorAvatarAction(creators);\r\n  });\r\n\r\n  it(\"fails to upload avatar if no files uploaded\", async () => {\r\n    const input = new AvatarInput(\"id\", {\r\n      filepath: \"/file.png\",\r\n      originalFilename: \"file.png\",\r\n      mimetype: \"image/png\",\r\n      size: 745823\r\n    });\r\n    creators.upload = jest.fn();\r\n\r\n    await action.execute(input);\r\n\r\n    expect(creators.upload).toHaveBeenCalledTimes(1);\r\n    expect(creators.upload).toHaveBeenCalledWith(expect.any(AvatarUpload));\r\n  });\r\n});\r\n"}, "__tests__/src/server/healthCheck/HealthCheckController.spec.ts": {"tests": [{"id": "28", "name": "HealthCheckController should return status UP with 200", "location": {"start": {"column": 4, "line": 10}}}], "source": "import { createMocks, MockResponse } from \"node-mocks-http\";\r\n// import HealthCheckController from \"../../../src/controllers/HealthCheckController\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession, RequestHandlerOptions } from \"@eait-playerexp-cn/server-kernel\";\r\nimport HealthCheckController from \"@src/server/healthCheck/HealthCheckController\";\r\n\r\ndescribe(\"HealthCheckController\", () => {\r\n  const options: RequestHandlerOptions = {};\r\n\r\n  it(\"should return status UP with 200\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();\r\n    res.status = jest.fn().mockReturnThis();\r\n    res.json = jest.fn().mockReturnThis();\r\n    const controller = new HealthCheckController(options);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res.status).toHaveBeenCalledWith(200);\r\n    expect(res.json).toHaveBeenCalledWith({ status: \"UP\" });\r\n  });\r\n});\r\n"}, "__tests__/src/shared/logging/TelemetryRecorder.spec.ts": {"tests": [{"id": "29", "name": "TelemetryRecorder should not add tracing fields when span context is invalid", "location": {"start": {"column": 4, "line": 19}}}], "source": "import { context, trace } from \"@opentelemetry/api\";\r\nimport TelemetryRecorder from \"@src/shared/logging/TelemetryRecorder\";\r\n\r\njest.mock(\"@opentelemetry/api\");\r\n\r\ndescribe(\"TelemetryRecorder\", () => {\r\n  let recorder;\r\n  let activity;\r\n\r\n  beforeEach(() => {\r\n    recorder = new TelemetryRecorder();\r\n    activity = { context: {} };\r\n  });\r\n\r\n  afterEach(() => {\r\n    jest.clearAllMocks(); // Clear mocks after each test\r\n  });\r\n\r\n  it(\"should not add tracing fields when span context is invalid\", () => {\r\n    const span = {\r\n      spanContext: jest.fn().mockReturnValue({\r\n        traceId: \"\",\r\n        spanId: \"\",\r\n        traceFlags: 0,\r\n        isRemote: false\r\n      })\r\n    };\r\n    (trace.getSpan as jest.Mock).mockReturnValue(span);\r\n    (context.active as jest.Mock).mockReturnValue({});\r\n\r\n    recorder.record(activity);\r\n\r\n    expect(activity.context).toEqual({});\r\n  });\r\n});\r\n"}, "__tests__/src/server/channels/discord/DiscordAccountHttpClient.spec.ts": {"tests": [{"id": "30", "name": "DiscordAccountHttpClient removes a Discord account", "location": {"start": {"column": 4, "line": 6}}}], "source": "import { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport DiscordAccountHttpClient from \"@src/server/channels/discord/DiscordAccountHttpClient\";\r\nimport Random from \"__tests__/factories/Random\";\r\n\r\ndescribe(\"DiscordAccountHttpClient\", () => {\r\n  it(\"removes a Discord account\", async () => {\r\n    const creatorId = Random.uuid();\r\n    const id = Random.uuid();\r\n    const client = { delete: jest.fn().mockReturnValue(Promise.resolve()) };\r\n    const discordClient = new DiscordAccountHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    discordClient.disconnectDiscordAccount(creatorId, id);\r\n\r\n    expect(client.delete).toHaveBeenCalledTimes(1);\r\n    expect(client.delete).toHaveBeenCalledWith(`/v1/creators/${creatorId}/discord-accounts/${id}`);\r\n  });\r\n});\r\n"}, "__tests__/src/shared/headers/ApplicationHeadersProvider.spec.ts": {"tests": [{"id": "31", "name": "ApplicationHeadersProvider adds authorization and telemetry headers", "location": {"start": {"column": 4, "line": 11}}}], "source": "import { MDC } from \"@eait-playerexp-cn/activity-logger\";\r\nimport ApplicationHeadersProvider from \"@src/shared/headers/ApplicationHeadersProvider\";\r\n\r\ndescribe(\"ApplicationHeadersProvider\", () => {\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n  });\r\n\r\n  afterEach(() => MDC.clear());\r\n\r\n  it(\"adds authorization and telemetry headers\", async () => {\r\n    const headersProvider = new ApplicationHeadersProvider();\r\n\r\n    const headers = await headersProvider.headers();\r\n\r\n    expect(headers).toEqual({\r\n      Accept: \"application/json\",\r\n      \"Content-Type\": \"application/json\",\r\n      \"x-client-id\": \"metadata-api\",\r\n      \"x-user-id\": \"guest\"\r\n    });\r\n  });\r\n});\r\n"}, "__tests__/src/server/channels/discord/ConnectedDiscordAccount.spec.ts": {"tests": [{"id": "32", "name": "ConnectedDiscordAccount shows disconnect discord account", "location": {"start": {"column": 4, "line": 4}}}], "source": "import ConnectedDiscordAccount from \"@src/server/channels/discord/ConnectedDiscordAccount\";\r\n\r\ndescribe(\"ConnectedDiscordAccount\", () => {\r\n  it(\"shows disconnect discord account\", async () => {\r\n    const creatorId = \"testCreatorId\";\r\n    const id = \"testId\";\r\n    const connectedDiscordAccount: ConnectedDiscordAccount = {\r\n      disconnectDiscordAccount: jest.fn()\r\n    };\r\n\r\n    await connectedDiscordAccount.disconnectDiscordAccount(creatorId, id);\r\n\r\n    expect(connectedDiscordAccount.disconnectDiscordAccount).toHaveBeenCalledWith(creatorId, id);\r\n  });\r\n});\r\n"}, "__tests__/src/pages/index.spec.tsx": {"tests": [{"id": "33", "name": "Index renders Index component", "location": {"start": {"column": 4, "line": 6}}}], "source": "import { render, screen } from \"@testing-library/react\";\r\nimport React from \"react\";\r\nimport Index from \"../../../src/pages/index\";\r\n\r\ndescribe(\"Index\", () => {\r\n  it(\"renders Index component\", async () => {\r\n    render(<Index />);\r\n\r\n    expect(await screen.findByText(/Onboarding Module Federation/i)).toBeInTheDocument();\r\n  });\r\n});\r\n"}}, "projectRoot": "C:\\Users\\<USER>\\CreatorNetwork\\onboarding-micro-frontend", "config": {"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "_comment": "This config was generated using 'stryker init'. Please see the guide for more information: https://stryker-mutator.io/docs/stryker-js/guides/react", "testRunner": "jest", "allowEmpty": true, "incremental": true, "jest": {"projectType": "custom", "configFile": "jest.config.js", "enableFindRelatedTests": false}, "mutate": ["src/pages/index.tsx", "src/components/**", "src/middleware/**", "src/server/**"], "ignorePatterns": ["reports", "stories", "src/pages/api/**", "src/instrumentation*", "src/pages/_app.tsx", "src/pages/_document.tsx", "src/utils/**"], "thresholds": {"high": 90, "low": 70, "break": 17.5}, "coverageAnalysis": "perTest", "reporters": ["progress", "clear-text", "html"], "checkers": ["typescript"], "tsconfigFile": "tsconfig.json", "concurrency": 5, "timeoutMS": 120000, "tempDirName": "strykerTmp", "cleanTempDir": "always", "allowConsoleColors": true, "checkerNodeArgs": [], "commandRunner": {"command": "npm test"}, "clearTextReporter": {"allowColor": true, "allowEmojis": false, "logTests": true, "maxTestsToLog": 3, "reportTests": true, "reportMutants": true, "reportScoreTable": true, "skipFull": false}, "dashboard": {"baseUrl": "https://dashboard.stryker-mutator.io/api/reports", "reportType": "full"}, "dryRunOnly": false, "eventReporter": {"baseDir": "reports/mutation/events"}, "ignoreStatic": false, "incrementalFile": "reports/stryker-incremental.json", "force": false, "fileLogLevel": "off", "inPlace": false, "logLevel": "info", "maxConcurrentTestRunners": 9007199254740991, "maxTestRunnerReuse": 0, "mutator": {"plugins": null, "excludedMutations": []}, "plugins": ["@stryker-mutator/*"], "appendPlugins": [], "htmlReporter": {"fileName": "reports/mutation/mutation.html"}, "jsonReporter": {"fileName": "reports/mutation/mutation.json"}, "disableTypeChecks": true, "symlinkNodeModules": true, "testRunnerNodeArgs": [], "timeoutFactor": 1.5, "dryRunTimeoutMinutes": 5, "warnings": true, "disableBail": false, "ignorers": [], "typescriptChecker": {"prioritizePerformanceOverAccuracy": true}}, "framework": {"name": "StrykerJS", "version": "8.5.0", "branding": {"homepageUrl": "https://stryker-mutator.io", "imageUrl": "data:image/svg+xml;utf8,%3Csvg viewBox='0 0 1458 1458' xmlns='http://www.w3.org/2000/svg' fill-rule='evenodd' clip-rule='evenodd' stroke-linejoin='round' stroke-miterlimit='2'%3E%3Cpath fill='none' d='M0 0h1458v1458H0z'/%3E%3CclipPath id='a'%3E%3Cpath d='M0 0h1458v1458H0z'/%3E%3C/clipPath%3E%3Cg clip-path='url(%23a)'%3E%3Cpath d='M1458 729c0 402.655-326.345 729-729 729S0 1131.655 0 729C0 326.445 326.345 0 729 0s729 326.345 729 729' fill='%23e74c3c' fill-rule='nonzero'/%3E%3Cpath d='M778.349 1456.15L576.6 1254.401l233-105 85-78.668v-64.332l-257-257-44-187-50-208 251.806-82.793L1076.6 389.401l380.14 379.15c-19.681 367.728-311.914 663.049-678.391 687.599z' fill-opacity='.3'/%3E%3Cpath d='M753.4 329.503c41.79 0 74.579 7.83 97.925 25.444 23.571 18.015 41.69 43.956 55.167 77.097l11.662 28.679 165.733-58.183-14.137-32.13c-26.688-60.655-64.896-108.61-114.191-144.011-49.329-35.423-117.458-54.302-204.859-54.302-50.78 0-95.646 7.376-134.767 21.542-40.093 14.671-74.09 34.79-102.239 60.259-28.84 26.207-50.646 57.06-65.496 92.701-14.718 35.052-22.101 72.538-22.101 112.401 0 72.536 20.667 133.294 61.165 182.704 38.624 47.255 98.346 88.037 179.861 121.291 42.257 17.475 78.715 33.125 109.227 46.994 27.193 12.361 49.294 26.124 66.157 41.751 15.309 14.186 26.497 30.584 33.63 49.258 7.721 20.214 11.16 45.69 11.16 76.402 0 28.021-4.251 51.787-13.591 71.219-8.832 18.374-20.171 33.178-34.523 44.219-14.787 11.374-31.193 19.591-49.393 24.466-19.68 5.359-39.14 7.993-58.69 7.993-29.359 0-54.387-3.407-75.182-10.747-20.112-7.013-37.144-16.144-51.259-27.486-13.618-11.009-24.971-23.766-33.744-38.279-9.64-15.8-17.272-31.924-23.032-48.408l-10.965-31.376-161.669 60.585 10.734 30.124c10.191 28.601 24.197 56.228 42.059 82.748 18.208 27.144 41.322 51.369 69.525 72.745 27.695 21.075 60.904 38.218 99.481 51.041 37.777 12.664 82.004 19.159 132.552 19.159 49.998 0 95.818-8.321 137.611-24.622 42.228-16.471 78.436-38.992 108.835-67.291 30.719-28.597 54.631-62.103 71.834-100.642 17.263-38.56 25.923-79.392 25.923-122.248 0-54.339-8.368-100.37-24.208-138.32-16.29-38.759-38.252-71.661-65.948-98.797-26.965-26.418-58.269-48.835-93.858-67.175-33.655-17.241-69.196-33.11-106.593-47.533-35.934-13.429-65.822-26.601-89.948-39.525-22.153-11.868-40.009-24.21-53.547-37.309-11.429-11.13-19.83-23.678-24.718-37.664-5.413-15.49-7.98-33.423-7.98-53.577 0-40.883 11.293-71.522 37.086-90.539 28.443-20.825 64.985-30.658 109.311-30.658z' fill='%23f1c40f' fill-rule='nonzero'/%3E%3Cpath d='M720 0h18v113h-18zM1458 738v-18h-113v18h113zM720 1345h18v113h-18zM113 738v-18H0v18h113z'/%3E%3C/g%3E%3C/svg%3E"}, "dependencies": {"@stryker-mutator/jest-runner": "8.5.0", "@stryker-mutator/typescript-checker": "8.5.0", "jest": "29.7.0", "typescript": "5.5.4", "webpack": "5.94.0"}}}