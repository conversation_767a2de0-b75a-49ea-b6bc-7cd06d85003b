import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import CachedTermsAndConditions from "../pactSafe/CachedTermsAndConditions";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";

@Service()
class ClearTermsAndConditionsStatusController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly termsAndConditions: CachedTermsAndConditions
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const identity = this.identity(req);
    const locale = this.locale(req);
    const { program } = req.query as { program: string };
    await this.termsAndConditions.clearSignedStatusForProgram(identity.id, locale, program);

    this.json(res, {});
  }
}

export default ClearTermsAndConditionsStatusController;
