import { AuthenticatedRequestHand<PERSON> } from "@eait-playerexp-cn/identity";
import { NextApiResponse } from "next";
import FacebookPageCredentials from "./FacebookPageCredentials";
import { Inject, Service } from "typedi";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";
import ConnectedAccountsHttpClient from "../../connecteAccounts/ConnectedAccountsHttpClient";

@Service()
class ConnectFacebookPageController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly connectedAccounts: ConnectedAccountsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const nucleusId = this.session(req, "nucleusId") as string;
    const { pageId, pageAccessToken } = req.body;
    let credentials;

    if (nucleusId) {
      credentials = FacebookPageCredentials.forInterestedCreator(
        pageId as string,
        pageAccessToken as string,
        nucleusId
      );
    } else {
      const identity = this.identity(req);
      credentials = FacebookPageCredentials.forCreator(pageId as string, pageAccessToken as string, identity.id);
    }

    await this.removeFromSession(req, "fbPages");

    await this.connectedAccounts.connectFacebookPage(credentials);

    await this.addToSession(req, "accountType", "Facebook");

    this.empty(res, HttpStatus.OK);
  }
}

export default ConnectFacebookPageController;
