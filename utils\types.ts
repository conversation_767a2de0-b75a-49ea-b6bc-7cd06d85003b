type CreatorIdentity = {
  locale: string;
  page: string;
};

export type BrowserAnalytics = {
  startedCreatorApplication?: (identity: CreatorIdentity) => void;
};

export type State = {
  [key: string]: string | number | boolean | null | object | [];
};

export type CreatorType = "CREATOR" | "INTERESTED_CREATOR";

export type AuthenticatedUser = {
  analyticsId: string | undefined;
  needsMigration?: boolean;
  username: string;
  status?: string;
  avatar?: string;
  tier?: string;
  isPayable?: boolean;
  type?: CreatorType;
  isFlagged?: boolean;
};

export type Country = {
  value: string;
  label: string;
  name: string;
};

export type Language = {
  value: string;
  label: string;
  id: string;
};

export type ContentUrl = {
  url: string;
  followers: number;
};

export type FranchiseType = "PRIMARY" | "SECONDARY";

export type PreferredFranchise = {
  id: string;
  type: FranchiseType;
};
