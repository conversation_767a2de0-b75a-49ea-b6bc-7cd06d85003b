.PHONY: help
help: ## Show help
	@echo Please specify a build target. The choices are:
	@grep -E '^[0-9a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

.PHONY: bootstrap
bootstrap: ## Create default .env file
	@echo "Creating default .env file..."
	@cp .env.dist .env
	@echo "Installing project dependencies"
	@npm ci

.PHONY: lint
lint: ## Check code format and validity
	@echo "Checking types.."
	@npm run test:types
	@echo "Checking coding standards..."
	@npm run format:check
	@echo "Checking linting rules..."
	@npm run lint 

.PHONY: check/ci
check/ci: # Run test suite with coverage
	@npm run test:coverage

.PHONY: mutation
mutation: # Run mutation test suite
	@npm run test:mutation

.PHONY: run
run: ## Run Next.js application
	@echo "Starting Next.js app..."
	@npm run dev

.PHONY: format
format: ## Format code
	@echo "Applying coding standard formatting..."
	@npm run format
	@npm run lint -- --fix

.PHONY: docs
docs: ## Start documentation container
	@echo "Starting container for documentation..."
	@docker-compose up -d docs

.PHONY: stop
stop: ## Stop Docker containers
	@echo "Stopping Docker containers..."
	@docker-compose stop

.PHONY: publish
publish: ## Publish Docker Image
	@echo "Building Docker image  $(REGISTRY):$(TAG)"
	@docker build -t $(REGISTRY):$(TAG) . $(BUILD_ARGS)
	@echo "Publishing Docker image $(REGISTRY):$(TAG)"
	@docker push $(REGISTRY):$(TAG)

.PHONY: login
login: ## Log in to GitLab Registry
	@echo "Log in to GitLab Registry"
	@docker login -u $(CN_USER) -p $(CN_GITLAB_KEY) registry.gitlab.ea.com
