// This file sets a custom webpack configuration to use your Next.js app
// with Sentry.
// https://nextjs.org/docs/api-reference/next.config.js/introduction
// https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/
const { withSentryConfig } = require("@sentry/nextjs");
const NextFederationPlugin = require("@module-federation/nextjs-mf");

module.exports = {
  output: "standalone",
  basePath: process.env.APP_BASE_PATH,
  webpack(config) {
    config.output.publicPath = `${process.env.APP_HOST}${process.env.APP_BASE_PATH}/_next/`;
    config.output.environment = {
      ...config.output.environment,
      dynamicImport: true
    };

    config.plugins.push(
      new NextFederationPlugin({
        name: "onboarding",
        filename: "static/chunks/onboarding-mfe.js",
        exposes: {
          "./FranchisesYouPlay": "./src/components/franchises-you-play/FranchisesYouPlay.tsx",
          "./CreatorType": "./src/components/creator-type/CreatorType.tsx",
          "./CreatorCode": "./src/components/creator-code/CreatorCode.tsx",
          "./TermsAndConditions": "./src/components/terms-and-conditions/TermsAndConditions.tsx",
          "./OnboardingInformationStep": "./src/components/information/OnboardingInformationStep.tsx",
          "./CommunicationPreferencesStep":
            "./src/components/communication-preferences-step/CommunicationPreferencesStep.tsx",
          "./MarketplaceDisplayNameStep": "./src/components/marketplace-display-name/MarketplaceDisplayNameStep.tsx"
        }
      })
    );

    return config;
  },
  reactStrictMode: false,
  sentry: {
    hideSourceMaps: true
  },
  publicRuntimeConfig: {
    APP_ENV: process.env.APP_ENV
  },
  experimental: {
    instrumentationHook: true,
    serverComponentsExternalPackages: ["@opentelemetry/sdk-node", "next-connect"]
  }
};

module.exports = withSentryConfig(module.exports, { silent: true });
