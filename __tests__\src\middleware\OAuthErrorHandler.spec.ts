import "reflect-metadata";
import { NextApiResponse } from "next";
import { ActivityFeed } from "@eait-playerexp-cn/activity-feed";
import { AxiosError } from "axios";
import { HttpRequest, HttpUrl } from "@eait-playerexp-cn/http";
import { LogLevel } from "@eait-playerexp-cn/activity-logger";
import { NextApiRequestWithMultipartFile } from "@eait-playerexp-cn/server-kernel";
import onOAuthError from "@src/middleware/OAuthErrorHandler";
import ApiContainer from "../../../src/ApiContainer";

jest.mock("../../../src/ApiContainer");

describe("onOAuthError", () => {
  const errorMessage = "Cannot view terms and conditions url. Invalid input provided";
  const problem = {
    title: "Unprocessable Entity",
    status: 422,
    type: "https://tools.ietf.org/html/rfc4918#section-11.2",
    code: "view-terms-and-conditions-url-invalid-input",
    errors: {
      email: ["must not be blank"]
    }
  };
  const config = { headers: {}, method: "get", baseURL: "https://example.com", url: "/creators" };
  const req = {
    method: "GET",
    [Symbol.for("NextInternalRequestMeta")]: { initURL: "https://localhost:3000/" },
    session: { set: jest.fn(), save: jest.fn() }
  } as unknown as NextApiRequestWithMultipartFile;
  const res = { end: jest.fn() } as unknown as NextApiResponse;
  const errorsWithApiProblem = [
    ["message", { config, response: { data: { ...problem, message: errorMessage } } } as unknown as AxiosError],
    ["detail", { config, response: { data: { ...problem, detail: errorMessage } } } as unknown as AxiosError]
  ];
  const feed = { add: jest.fn() } as unknown as ActivityFeed;

  beforeEach(() => {
    jest.clearAllMocks();
    (ApiContainer.get as jest.Mock).mockImplementation(() => feed);
  });

  test.each(errorsWithApiProblem)(
    "saves API Problem response with '%s' property in session and closes the current window",
    async (_property, error) => {
      await onOAuthError(error as AxiosError, req, res);

      expect(feed.add).toHaveBeenCalledTimes(0); // Since Axios errors have already been logged
      expect(req.session.error).toEqual({
        code: "view-terms-and-conditions-url-invalid-input",
        message: errorMessage
      });
      expect(req.session.save).toHaveBeenCalledTimes(1);
      expect(req.session.save).toHaveBeenCalledWith();
      expect(res.end).toHaveBeenCalledTimes(1);
      expect(res.end).toHaveBeenCalledWith("<script>window.close();</script>");
    }
  );

  it("saves `AxiosError` without response error message in session and closes the current window", async () => {
    const error = { config, message: "Connection timed out" } as AxiosError;
    await onOAuthError(error, req, res);

    expect(feed.add).toHaveBeenCalledTimes(1);
    expect(feed.add).toHaveBeenCalledWith({
      context: {
        exception: error,
        identifier: "application-error",
        request: new HttpRequest("GET", new HttpUrl(new URL("https://localhost:3000/")))
      },
      level: LogLevel.ERROR,
      message: "Connection timed out"
    });
    expect(req.session.error).toEqual("Connection timed out");
    expect(req.session.save).toHaveBeenCalledTimes(1);
    expect(req.session.save).toHaveBeenCalledWith();
    expect(res.end).toHaveBeenCalledTimes(1);
    expect(res.end).toHaveBeenCalledWith("<script>window.close();</script>");
  });

  it("saves `Error` message in session and closes the current window", async () => {
    const error = new Error();
    await onOAuthError(error, req, res);

    expect(feed.add).toHaveBeenCalledTimes(1);
    expect(feed.add).toHaveBeenCalledWith({
      context: {
        exception: error,
        identifier: "application-error",
        request: new HttpRequest("GET", new HttpUrl(new URL("https://localhost:3000/")))
      },
      level: LogLevel.ERROR,
      message: "No message provided"
    });
    expect(req.session.error).toEqual("No message provided");
    expect(req.session.save).toHaveBeenCalledTimes(1);
    expect(req.session.save).toHaveBeenCalledWith();
    expect(res.end).toHaveBeenCalledTimes(1);
    expect(res.end).toHaveBeenCalledWith("<script>window.close();</script>");
  });
});
