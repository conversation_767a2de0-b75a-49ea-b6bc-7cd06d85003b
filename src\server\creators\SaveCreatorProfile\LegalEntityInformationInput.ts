type EntityTypeFormValues = { value: string };

export type LegalEntityInformationFormValues = {
  entityType: EntityTypeFormValues | string;
  businessName: string;
  street: string;
  city: string;
  country: { value: string; label: string };
  state: string;
  zipCode: string;
};

export default class LegalEntityInformationInput {
  readonly entityType: string;
  readonly businessName?: string;
  readonly street: string;
  readonly addressLine2?: string;
  readonly city: string;
  public country: { readonly code: string; readonly name: string };
  readonly state: string;
  readonly zipCode: string;

  constructor(values: LegalEntityInformationFormValues) {
    this.entityType = (values?.entityType as EntityTypeFormValues)?.value || (values?.entityType as string);
    this.businessName = this.entityType === "INDIVIDUAL" ? null : values.businessName;
    this.street = values.street;
    this.city = values.city;
    this.country = { code: values?.country?.value, name: values?.country?.label };
    this.state = values.state;
    this.zipCode = values.zipCode;
  }
}
