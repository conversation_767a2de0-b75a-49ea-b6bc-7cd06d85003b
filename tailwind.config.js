const coreUiKit = require("@eait-playerexp-cn/core-ui-kit/config");

module.exports = coreUiKit({
  content: ["src/pages/**/*.{ts,js,jsx,tsx}", "src/components/**/*.{ts,js,jsx,tsx}"],
  theme: {
    extend: {
      fill: (theme) => ({
        current: "currentColor",
        "gray-5": theme("colors.gray.5"),
        "gray-50": theme("colors.gray.50"),
        "primary-50": theme("colors.primary.50"),
        "secondary-70": theme("colors.secondary.70"),
        "secondary-90": theme("colors.secondary.90"),
        "success-30": theme("colors.success.30"),
        "success-70": theme("colors.success.70")
      })
    },
    stroke: (theme) => ({
      "secondary-50": theme("colors.secondary.50"),
      "secondary-70": theme("colors.secondary.70"),
      "secondary-90": theme("colors.secondary.90"),
      "success-50": theme("colors.success.50"),
      "success-70": theme("colors.success.70")
    })
  },
  plugins: [require("@tailwindcss/aspect-ratio"), require("@tailwindcss/typography")]
});
