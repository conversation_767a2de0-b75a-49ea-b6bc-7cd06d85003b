import { createMocks, MockResponse } from "node-mocks-http";
// import HealthCheckController from "../../../src/controllers/HealthCheckController";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import HealthCheckController from "@src/server/healthCheck/HealthCheckController";

describe("HealthCheckController", () => {
  const options: RequestHandlerOptions = {};

  it("should return status UP with 200", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks();
    res.status = jest.fn().mockReturnThis();
    res.json = jest.fn().mockReturnThis();
    const controller = new HealthCheckController(options);

    await controller.handle(req, res);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({ status: "UP" });
  });
});
