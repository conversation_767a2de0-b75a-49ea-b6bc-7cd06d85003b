import ConnectedDiscordAccount from "./ConnectedDiscordAccount";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
export default class DiscordAccountHttpClient implements ConnectedDiscordAccount {
  constructor(@Inject("communicationsClient") private client: TraceableHttpClient) {}

  async disconnectDiscordAccount(creatorId: string, id: string): Promise<void> {
    await this.client.delete(`/v1/creators/${creatorId}/discord-accounts/${id}`);
  }
}
