---
currentMenu: howto
---

# module-federation

Module Federation [docs](https://www.npmjs.com/package/@module-federation/nextjs-mf).

Create component you would like to export in the components directory like so:

```
src
|--components
  ├── RemoteComponent.tsx
```

## Federate Component (Remote)

In next.config.js, expose each component you would like to federate

```
  webpack(config, options) {
    const { isServer } = options;
    config.plugins.push(
      new NextFederationPlugin({
        name: "remote",
        filename: "static/chunks/remoteEntry.js",
        exposes: {
          "./RemoteComponent": "./src/components/RemoteComponent.tsx"
        }
      })
    );
    return config;
  },
```

### Consuming Components (Host)

In the consuming repository, consume from the remote as such by adding to the `next-config.js`

```
webpack(config) {
  config.plugins.push(
    new NextFederationPlugin({
      name: 'host',
      filename: 'other.js',
      remotes: {
        remote: `remote@http://localhost:3002/_next/static/chunks/remoteEntry.js`,
      }
    }),
  );

  return config;
},
```
