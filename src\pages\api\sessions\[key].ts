import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import { createRouter } from "next-connect";
import onError from "@src/middleware/OAuthErrorHandler";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import ClearSessionsKeyController from "@src/server/connecteAccounts/ClearSessionsKeyController";
import withCors from "@src/middleware/WithCors";
import corsPreflight from "@src/middleware/CorsPreflight";
import Session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(Session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .delete(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(ClearSessionsKeyController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
