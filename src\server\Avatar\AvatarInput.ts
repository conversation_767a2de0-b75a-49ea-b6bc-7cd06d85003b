import AvatarUpload from "./AvatarUpload";
import fs from "fs";
import { FormDataBody } from "@eait-playerexp-cn/http";
import { UploadedFile } from "@eait-playerexp-cn/server-kernel";

export default class AvatarInput {
  constructor(
    private readonly creatorId: string,
    private readonly avatar: UploadedFile
  ) {}

  uploadedImage(): AvatarUpload {
    const formData = new FormDataBody();
    formData.append("avatar", fs.createReadStream(this.avatar.filepath), {
      knownLength: this.avatar.size,
      filename: this.avatar.originalFilename,
      contentType: this.avatar.mimetype
    });

    return new AvatarUpload(this.creatorId, formData);
  }
}
