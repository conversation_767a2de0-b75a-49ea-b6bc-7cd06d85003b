---
currentMenu: howto
---

# Add new pages

Next.js provides [file-based routing](https://nextjs.org/docs/routing/introduction).

```
src
|--pages
  ├── api
  │   └── testing-api.js
  ├── 404.js
  ├── communication-preferences.js
  ├── dashboard.js
  ├── opportunities
  │   ├── [id].js
  │   └── index.js
  ├── terms-and-conditions.js
  └── trust-and-safety-guidelines.js
```

Where pages can be accessed by file name:

- `/api/testing-api`,
- `/dashboard`,
- `/opportunities/d708c0af-c99c-465c-ab26-0a66ae3f4d53` for instance.

In order to keep the codebase **maintainable** and **testable** the following sections will provide guidelines on how to structure code.

## Pass feature flags as props to pages

Feature flags allow you to introduce new code without disrupting existing code.
In the example in the snippet below we're showing a different layout for a page, based on a feature flag.

<pre data-line="2,4-5,18"><code class="language-tsx line-numbers">
// imports...
import flags from "./feature-flags";

export default function Opportunity({ user, OPPORTUNITY_WITH_PERKS }) {
  return OPPORTUNITY_WITH_PERKS ? (
    &lt;OpportunityPageWithPerks user={user} /&gt;
  ) : (
    &lt;OpportunityPage user={user} /&gt;
  );
}

export const getServerSideProps = async ({ req, res, locale }) => {
  // ...

  return {
    props: {
      user,
      OPPORTUNITY_WITH_PERKS: flags.arePerksEnabled(),
      ...(await serverSideTranslations(locale, ["common", "opportunities"]))
    }
  };
};
</code></pre>

This way, we can unit test old and new page layouts in different test files `OpportunityPageWithPerks.spec.js` and `OpportunityPage.spec.js` and their only dependency is the current user which can easily be passed as a prop.

The implementation of `flags` can be as simple as follows

```ts
// imports...

function arePerksEnabled(): boolean {
  return config.OPPORTUNITY_WITH_PERKS;
}

export default { arePerksEnabled };
```

### Examples

- [pages/index](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/blob/main/pages/index.js)
- [pages/interested-creators/already-applied](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/blob/main/pages/interested-creators/already-applied.tsx)
- [pages/interested-creators/complete](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/blob/main/pages/interested-creators/complete.tsx)

## Keep layout components outside page components

Adding to our example, let's suppose our pages are shown inside a common layout.
We would keep the layout components at the Next.js page level in order no to overcomplicate the page component unit tests.

<pre data-line="7,12"><code class="language-tsx line-numbers">
// imports...

export default function Opportunity({ user, OPPORTUNITY_WITH_PERKS }) {
  // Translation code...

  return (
    &lt;Layout user={user} pageTitle={opportunity?.title}&gt;
      OPPORTUNITY_WITH_PERKS ? (
      &lt;OpportunityPageWithPerks user={user} opportunitiesLabels={opportunitiesLabels} /&gt;
      ) : (
      &lt;OpportunityPage user={user} opportunitiesLabels={opportunitiesLabels} /&gt;)
    &lt;/Layout&gt;
  );
}

// Server side code...
</code></pre>

### Examples

- [pages/interested-creators/information](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/blob/main/pages/interested-creators/information.tsx)

## Try to use TypeScript whenever possible

If your component doesn't depend on plain javascript code please use TypeScript.

On the other hand, if your code **depends on un-typed code**, and you think there's enough time to migrate it, **please migrate it**.

If the code you think can be migrated doesn't have tests, please **add tests before migrating it**.

For instance, if your new page depends on `useAppContext` hook, this is how your commit history would look like.

```
test(CRNE-1234) Describe `useAppContext`
refactor(CRNE-1234) Migrate `useAppContext` to TS
feature(CRNE-1234) Applicant welcome page
```

Where the second and third commit would use [TypeScript](https://www.typescriptlang.org/docs/) only
