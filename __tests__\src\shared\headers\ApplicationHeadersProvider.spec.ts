import { MDC } from "@eait-playerexp-cn/activity-logger";
import ApplicationHeadersProvider from "@src/shared/headers/ApplicationHeadersProvider";

describe("ApplicationHeadersProvider", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => MDC.clear());

  it("adds authorization and telemetry headers", async () => {
    const headersProvider = new ApplicationHeadersProvider();

    const headers = await headersProvider.headers();

    expect(headers).toEqual({
      Accept: "application/json",
      "Content-Type": "application/json",
      "x-client-id": "metadata-api",
      "x-user-id": "guest"
    });
  });
});
