TAG=latest
ARTIFACTORY=docker.artifacts.ea.com
APP_ENV=docker
SENTRY_DSN=
SUPPORTED_LOCALES=["en-us"]
HTTP_REQUEST_TIMEOUT=60000
LOG_LEVEL=DEBUG
CACHE_PREFIX=web_dev_
REDIS_PORT=6379
REDIS_HOST=localhost
REDIS_SCALE_READ=slave
COOKIE_PASSWORD=
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=lax
COOKIE_DOMAIN=
COOKIE_SECURE=false
SESSION_TTL=80000
FLAG_OBSERVABILITY=false
OTLP_TRACE_EXPORTER_URL=http://localhost:4317/v1/traces
API_CLIENT_ID=
API_CLIENT_SECRET=
DEFAULT_FRANCHISE_IMAGE=https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/public-images/franchises/ea-no-franchise.png
CONTENT_SCANNING_API_BASE_URL=https://dev-services.cn.ea.com/cn-content-scanning-api/
CONTENT_SUBMISSION_BASE_URL=https://dev-services.cn.ea.com/cn-content-submission-api/
OPERATIONS_API_BASE_URL=https://dev-intrnl-services.cn.ea.com/cn-operations-api
LEGAL_API_BASE_URL=https://dev-services.cn.ea.com/cn-legal-api
ACCESS_TOKEN_BASE_URL=https://dev-services.cn.ea.com/security
METADATA_API_BASE_URL=https://dev-services.cn.ea.com/metadata
APP_DEBUG=true
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOWED_METHODS=GET,DELETE,PATCH,POST,PUT
CORS_ALLOWED_ORIGINS=http://localhost:3040/support-a-creator,http://localhost:3050/
APP_HOST=http://localhost:3002
APP_BASE_PATH=/cn-onboarding-mfe
CORS_ALLOWED_HEADERS=traceparent,x-correlation-id,content-type
FACEBOOK_SCOPES=email,public_profile,read_insights,pages_show_list,pages_read_engagement,business_management
INSTAGRAM_STATE=d5fb3181be01ccd936bce45a776836c6
ONBOARDING_MFE_NAME=cn-onboarding-mfe
TERMS_STATUS_CACHE_TTL=86400
PROGRAM_CODE="support_a_creator"
DEVICE_TYPE=web
PLATFORM=PC
SESSION_PROXY=false
COMMUNICATIONS_API_BASE_URL=https://dev-services.cn.ea.com/cn-communications-api
INTERESTED_CREATOR_REAPPLY_PERIOD=true
