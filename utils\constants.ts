export const REGEX_PATTERN = {
  EMAIL:
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
};

export const validationConfig = {
  MAXLENGTH: 50,
  MAXLENGTH_20: 20,
  MAXLENGTH_40: 40,
  MAXLENGTH_80: 80,
  MAXLENGTH_255: 255,
  DATE_MAXLENGTH: 10,
  regex: {
    EMAIL: REGEX_PATTERN.EMAIL
  },
  MAX_FILE_SIZE: 524288000
};

export const SESSION_USER = "SESSION_USER";

export const DEFAULT_LOCALE = "en-us";

export const ERROR = "ERROR";
export const INFO = "INFO";
export const WARNING = "WARNING";
export const SUCCESS = "SUCCESS";
export const VALIDATION_ERROR = "VALIDATION_ERROR";
export const DOMAIN_ERROR = "DOMAIN_ERROR";
