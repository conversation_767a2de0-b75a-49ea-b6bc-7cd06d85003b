export default interface SignedLegalDocuments {
  contracts: Array<SignedContract>;
  history: Array<SignedTermsAndConditions>;
}

export type SignedTermsAndConditions = {
  creatorId: string;
  termsAndConditionsId: string;
  acceptedOnDate: number;
  documentUrl: string;
  content: string | null;
};

export type SignedContract = {
  uploadedByName: string;
  opportunityName: string;
  label: string;
  signedOnDate: number;
  documentUrl: string;
  uploaded: boolean;
};
