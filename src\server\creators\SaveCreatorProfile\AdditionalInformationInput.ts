type HardwarePartnerFormValues = { value: string; label: string };

class HardwarePartnerInput {
  readonly id: string;
  readonly name: string;

  constructor(values: HardwarePartnerFormValues) {
    this.id = values.value;
    this.name = values.label;
  }
}

export type AdditionalInformationFormValues = {
  hoodieSize: { value: string };
  hardwarePartners: HardwarePartnerFormValues[];
};

export default class AdditionalInformationInput {
  readonly hoodieSize?: string;
  readonly hardwarePartners: Array<HardwarePartnerInput>;

  constructor(values: AdditionalInformationFormValues) {
    this.hoodieSize = values.hoodieSize.value || null;
    this.hardwarePartners = values.hardwarePartners.map((values) => new HardwarePartnerInput(values));
  }
}
