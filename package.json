{"private": true, "scripts": {"dev": "cross-env NEXT_PRIVATE_LOCAL_WEBPACK=true DEBUG=express-session next dev -p 3002", "build": "NEXT_PRIVATE_LOCAL_WEBPACK=true next build", "start": "NEXT_PRIVATE_LOCAL_WEBPACK=true next start -p 3002", "lint": "next lint --fix", "lint:check": "next lint", "lint:tests": "eslint __tests__", "test": "jest --runInBand=true --cache", "test:coverage": "jest --runInBand=true --coverage --cache", "test:mutation": "stryker run", "test:types": "tsc --noEmit", "test-watch": "jest --watch", "format:check": "prettier . --check", "format": "prettier . --write", "prepare": "husky", "lint-staged": "lint-staged"}, "dependencies": {"@eait-playerexp-cn/activity-feed": "2.0.0", "@eait-playerexp-cn/activity-logger": "1.1.0", "@eait-playerexp-cn/client-kernel": "1.0.0", "@eait-playerexp-cn/core-ui-kit": "8.26.5", "@eait-playerexp-cn/http": "1.0.0", "@eait-playerexp-cn/http-client": "1.4.3", "@eait-playerexp-cn/identity": "3.0.2", "@eait-playerexp-cn/identity-test-fixtures": "^1.1.0", "@eait-playerexp-cn/metadata-http-client": "1.0.0", "@eait-playerexp-cn/metadata-types": "1.0.1", "@eait-playerexp-cn/object-mapper": "1.0.0", "@eait-playerexp-cn/onboarding-ui": "1.11.1", "@eait-playerexp-cn/server-kernel": "7.3.0", "@eait-playerexp-cn/telemetry": "1.0.0", "@module-federation/nextjs-mf": "8.8.23", "@opentelemetry/core": "1.26.0", "@opentelemetry/exporter-trace-otlp-http": "0.53.0", "@opentelemetry/resources": "1.26.0", "@opentelemetry/sdk-node": "0.53.0", "@opentelemetry/sdk-trace-node": "1.26.0", "@opentelemetry/semantic-conventions": "1.27.0", "@sentry/nextjs": "7.88.0", "cache-manager": "^3.4.4", "cache-manager-ioredis": "^2.1.0", "classnames": "2.5.1", "dayjs": "^1.11.13", "formidable": "^3.5.2", "googleapis": "^144.0.0", "moment-timezone": "^0.5.45", "next": "14.2.25", "next-connect": "1.0.0", "react": "18.2.0", "react-dom": "18.2.0", "reflect-metadata": "0.2.2", "typedi": "0.10.0", "webpack": "5.94.0", "winston": "3.14.2"}, "devDependencies": {"@babel/core": "7.25.2", "@babel/plugin-proposal-decorators": "7.24.7", "@babel/plugin-proposal-private-methods": "7.18.6", "@commitlint/cli": "19.4.0", "@commitlint/config-conventional": "19.2.2", "@eait-playerexp-cn/metadata-test-fixtures": "1.0.1", "@stryker-mutator/core": "8.5.0", "@stryker-mutator/jest-runner": "8.5.0", "@stryker-mutator/typescript-checker": "8.5.0", "@tailwindcss/aspect-ratio": "0.4.2", "@tailwindcss/typography": "0.5.14", "@testing-library/jest-dom": "6.4.8", "@testing-library/react": "16.0.0", "@testing-library/user-event": "14.5.2", "@types/formidable": "^3.4.5", "@types/jest": "29.5.12", "@types/jest-axe": "3.5.9", "@types/minimist": "1.2.5", "@types/node": "22.5.0", "@types/react": "18.3.4", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "8.2.0", "autoprefixer": "10.4.20", "babel-loader": "9.1.3", "babel-plugin-inline-react-svg": "2.0.2", "babel-plugin-transform-typescript-metadata": "0.3.2", "cross-env": "7.0.3", "cssnano": "7.0.5", "eslint": "8.57.0", "eslint-config-next": "14.2.6", "eslint-plugin-testing-library": "6.3.0", "fishery": "2.2.2", "husky": "9.1.5", "jest": "29.7.0", "jest-axe": "9.0.0", "jest-environment-jsdom": "29.7.0", "jest-html-reporters": "3.1.7", "lint-staged": "15.2.9", "node-mocks-http": "1.16.0", "postcss": "8.4.41", "postcss-import": "16.1.0", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "0.6.6", "style-loader": "4.0.0", "tailwindcss": "3.4.10", "tsc-files": "1.1.4", "tsx": "4.17.0", "typescript": "5.5.4"}, "lint-staged": {"*.{ts,tsx}": ["tsc-files --noEmit --pretty", "eslint --quiet --cache --fix", "prettier --write", "jest --bail --findRelatedTests --passWithNoTests"], "*.{js,jsx}": ["eslint --quiet --cache --fix", "prettier --write", "jest --bail --findRelatedTests --passWithNoTests"], "*.json": ["prettier --write"], "*.css": ["prettier --write --parser css"]}}