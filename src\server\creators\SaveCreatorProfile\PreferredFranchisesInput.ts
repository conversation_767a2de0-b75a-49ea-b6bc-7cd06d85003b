type PreferredFranchiseFormValues = { value: string; label: string };

class PreferredFranchise {
  readonly id: string;
  readonly name: string;

  constructor(
    values: PreferredFranchiseFormValues,
    readonly type: string
  ) {
    this.id = values.value;
    this.name = values.label;
  }
}

export type PreferredFranchisesFormValues = {
  primaryFranchise: PreferredFranchiseFormValues;
  secondaryFranchise: PreferredFranchiseFormValues[];
};

export default class PreferredFranchisesInput {
  readonly values: PreferredFranchise[];

  constructor(values: PreferredFranchisesFormValues) {
    this.values = values.secondaryFranchise
      .filter(({ label }) => !!label)
      .map((values) => new PreferredFranchise(values, "SECONDARY"));
    this.values.push(new PreferredFranchise(values.primaryFranchise, "PRIMARY"));
  }
}
