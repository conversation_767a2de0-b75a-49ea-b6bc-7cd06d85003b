import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import ClearAccountTypeController from "@src/server/connecteAccounts/ClearAccountTypeController";

describe("ClearAccountTypeController", () => {
  let controller: ClearAccountTypeController;
  const options = {} as RequestHandlerOptions;
  const session = { save: jest.fn() };

  beforeEach(() => jest.clearAllMocks());

  it("removes account type from the session", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: "/api/account-types",
      session
    });
    controller = new ClearAccountTypeController(options);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session.accountType).toBeUndefined();
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });
});
