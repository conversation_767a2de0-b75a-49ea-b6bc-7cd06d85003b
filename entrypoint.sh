#!/bin/sh

# Convert JSON to env, use @sh to handle values with spaces correctly
#_cn_secrets_cn-onboarding_$env is AWS secret name /cn/secrets/cn-onboarding_$env. CSI driver will mount the secret to /tmp/secret/_cn_secrets_cn-onboarding_$env
#And _cn_secrets_cn-onboarding_$env is made available to the container as MOUNTED_AWS_SECRET_NAME
jq -r 'to_entries[] | [.key,(.value|@sh)] | join("=")' /tmp/secret/$MOUNTED_AWS_SECRET_NAME > .env

onboarding_url=$(jq -r '.APP_HOST' /tmp/secret/$MOUNTED_AWS_SECRET_NAME)$(jq -r '.APP_BASE_PATH' /tmp/secret/$MOUNTED_AWS_SECRET_NAME)
current_onboarding_url=http://localhost:3002/cn-onboarding-mfe

echo $current_onboarding_url
echo $onboarding_url

find . \( -name '*.js' -o -name '*.json' \) ! -path "./standalone/node_modules/*" -exec sed  -i  "s,${current_onboarding_url},${onboarding_url},g" {} \;

node server.js
