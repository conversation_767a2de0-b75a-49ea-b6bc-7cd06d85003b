import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import onError from "@src/middleware/OAuthErrorHandler";
import HealthCheckController from "@src/server/healthCheck/HealthCheckController";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router.get(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
  const controller = ApiContainer.get(HealthCheckController);
  await controller.handle(req, res);
});

export default router.handler({ onError });
