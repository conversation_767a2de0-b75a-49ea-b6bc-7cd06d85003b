import AvatarInput from "@src/server/Avatar/AvatarInput";
import AvatarUpload from "@src/server/Avatar/AvatarUpload";
import UploadCreatorAvatarAction from "@src/server/Avatar/UploadCreatorAvatarAction";
import CreatorsHttpClient from "@src/server/creators/CreatorsHttpClient";
import "reflect-metadata";

describe("UploadCreatorAvatarAction", () => {
  let creators: CreatorsHttpClient = null;
  let action: UploadCreatorAvatarAction = null;

  beforeEach(() => {
    creators = {} as CreatorsHttpClient;
    action = new UploadCreatorAvatarAction(creators);
  });

  it("fails to upload avatar if no files uploaded", async () => {
    const input = new AvatarInput("id", {
      filepath: "/file.png",
      originalFilename: "file.png",
      mimetype: "image/png",
      size: 745823
    });
    creators.upload = jest.fn();

    await action.execute(input);

    expect(creators.upload).toHaveBeenCalledTimes(1);
    expect(creators.upload).toHaveBeenCalledWith(expect.any(AvatarUpload));
  });
});
