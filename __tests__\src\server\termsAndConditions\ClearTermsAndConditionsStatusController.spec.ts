import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import CachedTermsAndConditions from "@src/server/pactSafe/CachedTermsAndConditions";
import ClearTermsAndConditionsStatusController from "@src/server/pactSafe/ClearTermsAndConditionsStatusController";
import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";

jest.mock("../../../../config");

describe("ClearTermsAndConditionsStatusController", () => {
  let controller: ClearTermsAndConditionsStatusController;
  const options = {} as RequestHandlerOptions;
  const session = { save: jest.fn() };

  it("clears signed status detail from cache", async () => {
    const identity = Identity.fromStored(aStoredIdentity());
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "DELETE",
      url: `/v2/terms-and-conditions/terms-and-conditions-status`,
      query: { program: "affiliate" }
    });
    req.session = { identity, save: jest.fn() };
    const signedStatus = { clearSignedStatusForProgram: jest.fn() };
    controller = new ClearTermsAndConditionsStatusController(
      options,
      signedStatus as unknown as CachedTermsAndConditions
    );

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(signedStatus.clearSignedStatusForProgram).toHaveBeenCalledTimes(1);
    expect(signedStatus.clearSignedStatusForProgram).toHaveBeenCalledWith(identity.id, "en-us", "affiliate");
  });
});
