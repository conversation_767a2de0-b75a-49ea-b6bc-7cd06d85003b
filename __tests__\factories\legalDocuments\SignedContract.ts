import { Factory } from "fishery";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Random } from "@eait-playerexp-cn/onboarding-ui";
import { SignedContract } from "@src/server/legalDocuments/SignedLegalDocuments";

const factory = Factory.define<SignedContract>(() => ({
  uploadedByName: Random.firstName(),
  opportunityName: Random.uuid(),
  label: Random.name,
  signedOnDate: LocalizedDate.epochMinusMonths(3),
  documentUrl: Random.url(),
  uploaded: Random.boolean()
}));

export function aSignedContract(override = {}): SignedContract {
  return factory.build(override);
}
