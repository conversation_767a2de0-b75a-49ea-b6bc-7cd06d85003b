---
currentMenu: howto
---

# Structure form components

Whenever a page contains a form, please create a new component for it and test it individually.
Otherwise, page component tests can become very complicated.

Think for instance on the preferred email form in the communication preferences page.

```tsx
const PreferredEmailForm = ({ rules, communicationPreferences, buttons, onChange, isSaved = false, isLoader }) => {
  // ...

  return (
    <Form key="email" mode="onChange" onSubmit={onSubmit}>
      Your form elements....
    </Form>
  );
};
```

There would be a file and a test file for it `PreferredEmailForm.tsx` and `PreferredEmailForm.spec.tsx`.

Examples of unit test cases

```js
it("shows error message if invalid email is provided");
it("shows error message if no email is provided");
it("disables save button if the form has an error");
it("pre-populates the form with current preferred email");
```

Another advantage of doing this is that we don't need to have validation rules in separate files

<pre data-line="9-21"><code class="language-tsx line-numbers">
const PreferredEmailForm = ({
  communicationPreferences,
  buttons,
  onChange,
  isSaved = false,
  isLoader
}) => {
  const rules = { 
    preferredEmail: {
      maxLength: { 
        value: validationConfig.MAXLENGTH_80,
        message: translations.messages.preferredEmailTooLong 
      },
      pattern: {
        value: validationConfig.regex.EMAIL,
        message: translations.messages.preferredEmailInvalid
      }
    }
  };
  // ...

  return (
    &lt;Form key="email" mode="onChange" onSubmit={onSubmit}&gt;
     Your form elements....
    &lt;/Form&gt;
  );
}
</code></pre>

Since you have the validation rules there, it will be more evident when one of them is not properly tested.

## Examples

- [components/forms/InterestedCreatorInformationForm](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/blob/main/components/forms/InterestedCreatorInformationForm.tsx)
