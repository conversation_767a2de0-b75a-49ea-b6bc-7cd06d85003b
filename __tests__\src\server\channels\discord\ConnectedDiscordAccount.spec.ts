import ConnectedDiscordAccount from "@src/server/channels/discord/ConnectedDiscordAccount";

describe("ConnectedDiscordAccount", () => {
  it("shows disconnect discord account", async () => {
    const creatorId = "testCreatorId";
    const id = "testId";
    const connectedDiscordAccount: ConnectedDiscordAccount = {
      disconnectDiscordAccount: jest.fn()
    };

    await connectedDiscordAccount.disconnectDiscordAccount(creatorId, id);

    expect(connectedDiscordAccount.disconnectDiscordAccount).toHaveBeenCalledWith(creatorId, id);
  });
});
