import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

type ValidatedCreatorCode = {
  creatorCode: string;
  creatorId: string;
  isValidCode: boolean;
};

@Service()
class ValidatedCreatorCodesHttpClient {
  constructor(@Inject("operationsClient") private readonly client: TraceableHttpClient) {}

  async validateCreatorCode(creatorCode: string, creatorId: string): Promise<ValidatedCreatorCode> {
    const response: AxiosResponse = await this.client.get(`/v2/creator-code/${creatorCode}`, { query: { creatorId } });
    return response.data;
  }
}

export default ValidatedCreatorCodesHttpClient;
