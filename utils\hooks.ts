import { useCallback, useEffect, useState } from "react";

export const useAsync = (
  asyncFunction: (data: unknown) => Promise<unknown>,
  immediate = true
): { execute: (data: unknown) => Promise<void>; pending: boolean; value: unknown; error: unknown } => {
  const [pending, setPending] = useState(false);
  const [value, setValue] = useState(null);
  const [error, setError] = useState(null);

  // The execute function wraps our function and
  // handles setting state for pending, value, and error.
  // useCallback ensures the below useEffect is not called
  // on every render, but only if asyncFunction changes.
  const execute = useCallback(
    (data?) => {
      setPending(true);
      setValue(null);
      setError(null);
      return asyncFunction(data)
        .then((response) => setValue(response))
        .catch((error) => setError(error))
        .finally(() => setPending(false));
    },
    [asyncFunction]
  );

  // Call execute if we want to fire it right away.
  // Otherwise execute can be called later, such as
  // in an onClick handler.
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  return { execute, pending, value, error };
};
