---
currentMenu: configuration
---

<!-- TOC -->

- [Configuration](#configuration)
  - [Redis Configuration](#redis-configuration)
  - [Session Configuration](#session-configuration)
  <!-- TOC -->

# Configuration

This application uses [Next.js environment variables](https://nextjs.org/docs/pages/building-your-application/configuring/environment-variables).
It provides a default `.env.dist` file that gets created when you set up this application for the first time using `make bootstrap`.

Some settings are omitted for security reasons, things like secrets or passwords you'll need to change manually.

In pre-production environments configuration is taken from an [AWS Secrets Manager](https://docs.aws.amazon.com/secretsmanager/latest/userguide/intro.html) secret.

Secrets names are configured per application and per environment and follow the naming convention shown below

```
/cn/secrets/{application-name}_{environment}
```

For this application the following secrets are used.

- `/cn/secrets/cn-onboarding-mfe_dev`
- `/cn/secrets/cn-onboarding-mfe_qa`
- `/cn/secrets/cn-onboarding-mfe_uat`
- `/cn/secrets/cn-onboarding-mfe_regress`

## Redis Configuration

We use a Redis cluster which is a managed [Amazon ElasticCache](https://aws.amazon.com/pm/elasticache/) cluster in AWS.

In your local development setup we'll use an SSH tunnel forwarding the Redis port.
So you can access the pre-production cluster as if Redis were running locally.

The default configuration for local setup will be as shown in the snippet below.

```properties
REDIS_PORT=6379
REDIS_HOST=localhost
REDIS_SCALE_READ=slave
```

For more details please check the corresponding [documentation](https://eait-playerexp-cn.gitlab.ea.com/-/creator-network-frontend/shared-kernel/server-kernel/docs/introduction.html#redis).

## Session Configuration

For session handling we use [Express Session](https://github.com/expressjs/session).
The default configuration for this application is shown in the snippet below.

```properties
COOKIE_PASSWORD=
COOKIE_HTTP_ONLY=true
COOKIE_SAME_SITE=lax
COOKIE_DOMAIN=
COOKIE_SECURE=false
SESSION_TTL=14400
SESSION_PROXY=false
```

We set `SESSION_SECURE` and `SESSION_PROXY` to `false` since we don't run the application locally over SSH.
`COOKIE_PASSWORD` value must be the same used in the Micro Frontends and Backends For Frontends, or you won't be able to share session data.

For more details please check the corresponding [documentation](https://eait-playerexp-cn.gitlab.ea.com/-/creator-network-frontend/shared-kernel/server-kernel/docs/introduction.html#sessions).
