import SignedStatus from "@src/server/pactSafe/SignedStatus";
import { Inject, Service } from "typedi";
import TermsAndConditions from "@src/server/pactSafe/TermsAndConditionsHttpClient";

@Service()
export default class CheckTermsAndConditionsStatusAction {
  constructor(
    @Inject("cachedTermsAndConditions")
    private readonly termsAndConditions: TermsAndConditions
  ) {}

  async execute(creatorId: string): Promise<SignedStatus> {
    return this.termsAndConditions.signedStatusWithProgram(creatorId);
  }
}
