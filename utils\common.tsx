import { IncomingHttpHeaders } from "http";
import { DEFAULT_LOCALE, DOMAIN_ERROR, ERROR, SUCCESS, VALIDATION_ERROR } from "./constants";
import React from "react";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

export function isAdult(birthDate: string): boolean {
  const dateOfBirth = LocalizedDate.fromFormattedDate(birthDate);
  return dateOfBirth.isAfter(LocalizedDate.subtractFromNow(18, "years"));
}

export function isString(obj: Record<string, unknown>): boolean {
  return Object.prototype.toString.call(obj) === "[object String]";
}

export function isObj(obj: Record<string, unknown>): boolean {
  return Object.prototype.toString.call(obj) === "[object Object]";
}

//------------------------------------------------
// Extract Locale from accept-language header
//------------------------------------------------
export function getLocale(headers: IncomingHttpHeaders, SUPPORTED_LOCALES: string | unknown[]): string {
  // If no browser accept-language then fallback to DEFAULT_LOCALE
  const browserLocale = ((headers &&
    headers["accept-language"] &&
    headers["accept-language"]?.match(/\w{2}\W\w{2}/)) || [DEFAULT_LOCALE])[0];
  return SUPPORTED_LOCALES.includes(browserLocale.toLowerCase()) ? browserLocale.toLowerCase() : DEFAULT_LOCALE;
}

export const onToastClose = (toast: string, dispatch: (arg0: { type: string; data: boolean }) => void): void => {
  if (toast === ERROR) dispatch({ type: ERROR, data: false });
  if (toast === SUCCESS) dispatch({ type: SUCCESS, data: false });
  if (toast === VALIDATION_ERROR) dispatch({ type: VALIDATION_ERROR, data: false });
  if (toast === DOMAIN_ERROR) dispatch({ type: DOMAIN_ERROR, data: false });
};

export function toastContent(validationError: Record<string, string>[]): JSX.Element {
  return (
    <ul className="formatted-content">
      {validationError.map((error, index) =>
        !Array.isArray(error.errorMessages) ? (
          <li className="text-gray-90" key={index}>{`${error.propertyName} ${error.errorMessages}`}</li>
        ) : (
          <li className="text-gray-90" key={index}>
            {error.errorMessages.length === 1 ? (
              `${error.propertyName} ${error.errorMessages[0]}`
            ) : (
              <>
                {error.propertyName}
                <ul key={"messages-" + index}>
                  {error.errorMessages.map((message, index) => (
                    <li key={"message-" + index}>{message}</li>
                  ))}
                </ul>
              </>
            )}
          </li>
        )
      )}
    </ul>
  );
}
