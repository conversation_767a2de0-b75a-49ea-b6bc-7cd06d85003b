import { Inject, Service } from "typedi";
import SignedLegalDocuments from "./SignedLegalDocuments";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

@Service()
export default class LegalDocumentsHttpClient {
  constructor(@Inject("legalClient") private readonly client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/legal/legal-api/docs/api.html#tag/Accepted-Terms-and-Conditions/paths/~1v1~1accepted-terms-and-conditions~1%7BcreatorId%7D/get Accepted Terms and conditions}
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/legal/legal-api/docs/api.html#tag/Signed-Contracts/operation/viewSignedContractsV2 Signed Contracts}
   */
  async allWithSignature(creatorId: string): Promise<SignedLegalDocuments> {
    const {
      data: { history = [] }
    } = (await this.client.get(`/v1/accepted-terms-and-conditions/${creatorId}`)) || {};

    const contracts = (await this.client.get(`/v2/signed-contracts`, { query: { creatorId } })).data || [];

    return Promise.resolve({ history, contracts });
  }
}
