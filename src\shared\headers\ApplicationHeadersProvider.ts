import { MDC } from "@eait-playerexp-cn/activity-logger";
import { <PERSON>ersProvider, HttpHeaders } from "@eait-playerexp-cn/http-client";

export default class ApplicationHeadersProvider implements HeadersProvider {
  headers(): Promise<HttpHeaders> {
    return Promise.resolve({
      Accept: "application/json",
      "Content-Type": "application/json",
      "x-client-id": "metadata-api",
      "x-user-id": MDC.get("userId") ? (MDC.get("userId") as string) : "guest"
    });
  }
}
