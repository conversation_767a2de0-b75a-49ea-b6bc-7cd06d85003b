import * as Sentry from "@sentry/nextjs";
import { Activity } from "@eait-playerexp-cn/activity-feed";
import SentryRecorder from "@src/shared/logging/SentryRecorder";

jest.mock("@sentry/nextjs");

describe("SentryRecorder", () => {
  const nonErrorActivities = [
    ["info", Activity.info("info-identifier", "Informational message", {})],
    ["warn", Activity.warn("warning-identifier", "Warning message", {})],
    ["debug", Activity.debug("debug-identifier", "Debugging message", {})]
  ];

  beforeEach(() => jest.clearAllMocks());

  it("records error activities", () => {
    const exception = new Error("undefined is not a function");
    const activity = Activity.error("activity-identifier", "Something wrong occurred", {
      exception,
      key: "value"
    });
    const recorder = new SentryRecorder();

    recorder.record(activity);

    expect(Sentry.captureException).toHaveBeenCalledTimes(1);
    expect(Sentry.captureException).toHaveBeenCalledWith(exception, {
      extra: {
        exception,
        identifier: "activity-identifier",
        key: "value"
      }
    });
  });

  test.each(nonErrorActivities)("ignores %s activities", (_level, activity: Activity) => {
    const recorder = new SentryRecorder();

    recorder.record(activity);

    expect(Sentry.captureException).toHaveBeenCalledTimes(0);
  });
});
