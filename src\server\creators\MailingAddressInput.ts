type CountryFormValues = { value: string; label: string };

export type MailingAddressFormValues = {
  country: CountryFormValues;
  street: string;
  city: string;
  state: string;
  zipCode: string;
};

export default class MailingAddressInput {
  readonly country: { readonly code: string; readonly name: string };
  readonly street: string;
  readonly city: string;
  readonly state: string;
  readonly zipCode: string;

  constructor(values: MailingAddressFormValues) {
    this.country = { code: values.country.value, name: values.country.label };
    this.street = values.street;
    this.city = values.city;
    this.state = values.state;
    this.zipCode = values.zipCode;
  }
}
