import "reflect-metadata";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import ConnectedAccountsHttpClient from "@src/server/connecteAccounts/ConnectedAccountsHttpClient";
import { Random } from "@eait-playerexp-cn/onboarding-ui";

describe("ConnectedAccountsHttpClient", () => {
  it("finds all connected accounts", async () => {
    const account = {
      name: Random.firstName(),
      disconnected: false,
      username: Random.userName(),
      id: Random.uuid(),
      type: "YOUTUBE",
      uri: Random.url(),
      thumbnail: Random.imageUrl(),
      isExpired: true,
      accountId: Random.uuid()
    };
    const connectedAccounts = [account];
    const nucleusId = "abc124476458fhgfghl";
    const client = { get: jest.fn().mockReturnValue({ data: connectedAccounts }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);

    const accounts = await connectedAccountsHttpClient.getAllConnectedAccounts(nucleusId);

    expect(accounts).toEqual(connectedAccounts);
    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith(`/v1/connected-accounts/${nucleusId}`);
  });

  it("connects a Facebook page", async () => {
    const nucleusId = "abc124476458fhgfghl";
    const credentials = {
      accessToken: Random.string(),
      creatorId: null,
      nucleusId,
      pageAccessToken: Random.string(),
      pageId: "a057717c-01f2-49de-9422-289b06649809"
    };
    const client = { post: jest.fn().mockReturnValue({ data: null }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);

    await connectedAccountsHttpClient.connectFacebookPage(credentials);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/v1/facebook-accounts", { body: credentials });
  });

  it("removes a connected account for an interested creator", async () => {
    const accountId = "a1LDF00000K4z0i2AB";
    const type = "INTERESTED_CREATOR";
    const client = { delete: jest.fn().mockReturnValue({ data: null }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);

    await connectedAccountsHttpClient.removeConnectedAccount(accountId, type);

    expect(client.delete).toHaveBeenCalledTimes(1);
    expect(client.delete).toHaveBeenCalledWith(`/v1/connected-accounts/${accountId}`, { query: { type } });
  });
});
