import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import onError from "@src/middleware/OAuthErrorHandler";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { verifySession } from "@eait-playerexp-cn/identity";
import ApiContainer from "@src/ApiContainer";
import ClearAccountTypeController from "@src/server/connecteAccounts/ClearAccountTypeController";
import corsPreflight from "@src/middleware/CorsPreflight";
import withCors from "@src/middleware/WithCors";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .delete(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(ClearAccountTypeController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
