import { context, trace } from "@opentelemetry/api";
import TelemetryRecorder from "@src/shared/logging/TelemetryRecorder";

jest.mock("@opentelemetry/api");

describe("TelemetryRecorder", () => {
  let recorder;
  let activity;

  beforeEach(() => {
    recorder = new TelemetryRecorder();
    activity = { context: {} };
  });

  afterEach(() => {
    jest.clearAllMocks(); // Clear mocks after each test
  });

  it("should not add tracing fields when span context is invalid", () => {
    const span = {
      spanContext: jest.fn().mockReturnValue({
        traceId: "",
        spanId: "",
        traceFlags: 0,
        isRemote: false
      })
    };
    (trace.getSpan as jest.Mock).mockReturnValue(span);
    (context.active as jest.Mock).mockReturnValue({});

    recorder.record(activity);

    expect(activity.context).toEqual({});
  });
});
