import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";
import ConnectedAccountsHttpClient from "./ConnectedAccountsHttpClient";

@Service()
class RemoveConnectedAccountController extends Request<PERSON><PERSON><PERSON> implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly connectedAccounts: ConnectedAccountsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const type: string = this.session(req, "nucleusId") ? "INTERESTED_CREATOR" : "CREATOR";
    const accountId = req.query.id as string;
    await this.connectedAccounts.removeConnectedAccount(accountId, type);
    this.empty(res, HttpStatus.OK);
  }
}

export default RemoveConnectedAccountController;
