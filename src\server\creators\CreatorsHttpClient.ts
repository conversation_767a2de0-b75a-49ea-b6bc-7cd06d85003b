import { AxiosResponse } from "axios";
import { Inject, Service } from "typedi";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import AvatarUpload from "@src/server/Avatar/AvatarUpload";
import { AccountInformation, CreatorWithProgramCode } from "@eait-playerexp-cn/onboarding-ui";
import CreatorCriteria from "./CreatorCriteria";

type UpdatedIdentity = {
  id: string;
  accountInformation: AccountInformation & {
    defaultGamerTag: string;
    originEmail: string;
    lastLoginDate: string;
  };
};

@Service()
class CreatorsHttpClient {
  constructor(@Inject("operationsClient") protected client: TraceableHttpClient) {}

  async matching(criteria: CreatorCriteria): Promise<Array<CreatorWithProgramCode>> {
    const response: AxiosResponse = await this.client.get("/v7/creators", { query: criteria });
    return response.data
      .filter((c) => c.accountInformation.nucleusId === criteria.nucleusId)
      .map((data) => CreatorWithProgramCode.fromApi(data));
  }

  async upload(uploadedImage: AvatarUpload): Promise<void> {
    await this.client.upload(`/v1/creators/${uploadedImage.id}/avatar`, { body: uploadedImage.avatar });
  }

  async syncIdentity(identity: UpdatedIdentity): Promise<void> {
    await this.client.put(`/v1/creators/${identity.id}`, { body: identity });
  }
}

export default CreatorsHttpClient;
