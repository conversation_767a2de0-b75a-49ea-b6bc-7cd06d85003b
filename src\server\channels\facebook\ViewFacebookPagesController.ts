import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { ApiProblem } from "@eait-playerexp-cn/api-problem";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class ViewFacebookPagesController extends Request<PERSON><PERSON>ler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const pages = this.session(req, "fbPages");

    if (!pages) {
      this.json(res, ApiProblem.from(HttpStatus.NOT_FOUND), HttpStatus.NOT_FOUND);
      return;
    }

    this.json(res, pages);
  }
}

export default ViewFacebookPagesController;
