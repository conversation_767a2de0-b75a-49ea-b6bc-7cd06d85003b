import { AccessToken, OAuthTokenProvider } from "@eait-playerexp-cn/http-client";
import { RedisCache } from "@eait-playerexp-cn/server-kernel";
import CachedAccessTokenProvider from "@src/shared/tokens/CachedAccessTokenProvider";

describe("CachedAccessTokenProvider", () => {
  let tokenProvider: OAuthTokenProvider;
  let cache: RedisCache;
  let cachedAccessTokenProvider: CachedAccessTokenProvider;

  beforeEach(() => {
    tokenProvider = { accessToken: jest.fn() } as unknown as OAuthTokenProvider;
    cache = { has: jest.fn(), get: jest.fn(), set: jest.fn() } as unknown as RedisCache;
    cachedAccessTokenProvider = new CachedAccessTokenProvider(tokenProvider, cache);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return access token from cache if available", async () => {
    const mockAccessToken = { token: "cached-token", expiresIn: 3600 } as AccessToken;
    (cache.has as jest.Mock).mockResolvedValue(true);
    (cache.get as jest.Mock).mockResolvedValue(mockAccessToken);

    const accessToken = await cachedAccessTokenProvider.accessToken();

    expect(accessToken).toEqual(mockAccessToken);
    expect(cache.has).toHaveBeenCalledWith("accessToken");
    expect(cache.get).toHaveBeenCalledWith("accessToken");
    expect(tokenProvider.accessToken).not.toHaveBeenCalled(); // Ensure provider was not called
  });

  it("should fetch a new access token and cache it if not available", async () => {
    const mockAccessToken = { token: "new-token", expiresIn: 3600 } as AccessToken;
    (cache.has as jest.Mock).mockResolvedValue(false);
    (tokenProvider.accessToken as jest.Mock).mockResolvedValue(mockAccessToken);

    const accessToken = await cachedAccessTokenProvider.accessToken();

    expect(accessToken).toEqual(mockAccessToken);
    expect(cache.has).toHaveBeenCalledWith("accessToken");
    expect(tokenProvider.accessToken).toHaveBeenCalled(); // Ensure provider was called
    expect(cache.set).toHaveBeenCalledWith("accessToken", mockAccessToken, mockAccessToken.expiresIn - 5);
  });
});
