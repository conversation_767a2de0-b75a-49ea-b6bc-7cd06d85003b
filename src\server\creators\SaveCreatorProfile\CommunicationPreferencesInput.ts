type LanguageFormValues = { code: string; name: string } & { value: string; label: string };

export type CommunicationPreferencesFormValues = {
  email: string;
  phone: string;
  contentLanguages: LanguageFormValues[];
  preferredLanguage: LanguageFormValues;
};

export default class CommunicationPreferencesInput {
  private readonly email: string;
  private phone: string;
  private contentLanguages: Array<{ code: string; name: string }>;
  private preferredLanguage: { code: string; name: string };

  constructor(values: CommunicationPreferencesFormValues) {
    this.email = values.email;
    this.phone = values.phone;
    this.contentLanguages = values.contentLanguages.length
      ? values.contentLanguages.map((item) => {
          return {
            code: item.value,
            name: item.label
          };
        })
      : [];
    this.preferredLanguage = values.preferredLanguage
      ? { code: values.preferredLanguage.value, name: values.preferredLanguage.label }
      : null;
  }
}
