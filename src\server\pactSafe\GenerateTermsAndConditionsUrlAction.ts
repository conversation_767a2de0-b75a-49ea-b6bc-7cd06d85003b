import { Inject, Service } from "typedi";
import TermsAndConditionsHttpClient from "@src/server/pactSafe/TermsAndConditionsHttpClient";
import SignerInformation from "@src/server/pactSafe/SignerInformation";

@Service()
export default class GenerateTermsAndConditionsUrlAction {
  constructor(
    @Inject("cachedTermsAndConditions")
    private readonly termsAndConditions: TermsAndConditionsHttpClient
  ) {}

  async getUrl(signer: SignerInformation): Promise<Record<string, unknown>> {
    return this.termsAndConditions.signerUrl(signer);
  }
}
