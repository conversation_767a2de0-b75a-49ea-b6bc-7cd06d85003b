import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

export default class Unknown<PERSON><PERSON> extends Error {
  public readonly originEmail: string;
  public readonly nucleusId: number;
  public readonly userName: string;
  public readonly dateOfBirth: string;

  constructor(originEmail: string, nucleusId: number, defaultGamerTag: string, dateOfBirth: number, message: string) {
    super(message);
    this.originEmail = originEmail;
    this.nucleusId = nucleusId;
    this.message = message;
    this.userName = defaultGamerTag;
    this.dateOfBirth = LocalizedDate.format(new Date(dateOfBirth), "YYYY-MM-DD");
  }

  public static withIdentity(
    originEmail: string,
    nucleusId: number,
    defaultGamerTag: string,
    dateOfBirth: number
  ): UnknownCreator {
    return new UnknownCreator(
      originEmail,
      nucleusId,
      defaultGamerTag,
      dateOfBirth,
      `Cannot find creator with Nucleus ID '${nucleusId}'`
    );
  }
}
