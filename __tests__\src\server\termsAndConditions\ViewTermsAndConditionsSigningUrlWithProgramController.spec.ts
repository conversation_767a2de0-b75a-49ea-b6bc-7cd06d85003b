import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import ViewTermsAndConditionsSigningUrlWithProgramController from "@src/server/pactSafe/ViewTermsAndConditionsSigningUrlWithProgramController";
import TermsAndConditionsHttpClient from "@src/server/pactSafe/TermsAndConditionsHttpClient";

describe("ViewTermsAndConditionsSigningUrlWithProgramController", () => {
  let controller: ViewTermsAndConditionsSigningUrlWithProgramController;
  const options = {} as RequestHandlerOptions;

  beforeEach(() => jest.clearAllMocks());

  it("get signing url for pact safe", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: `v2/terms-and-conditions/signing-url`
    });
    const signingUrl = {
      contractUrl:
        'https://app.pactsafe.com/sign?r=60d22c838ea821120cee5998&s=60c2f746704ffb0e40d92edc&signature=leSvpmgIQMLPi7Ua2Pl4Z0i5AkTRFzO6km3Q2FLMzeSkx9ZIRA7Bb59PCiFsv3vQ4oQTd-4~0kNsOMykCtwi5Vp9Qe9aqmaC~UNTPuwiCnhYSIdkfG88YkWUF1xdFDOZUWkcdPG-~sVLn8MAj8p0vtlYczeydgMsdVHWcRkjBYa3Z~BoqMs1bkt0m7tFovXsh2Aenos3CKaDB118ipQ1CbGmwcdbbWcVptIaqo0ES85cKV-5Cx~vEqnnO18uy6IdAeOsFsbKEQBh2kYFZymGdCgfv65fH6vof9hRaE--TCUnYR~QZm01uO1Cpb02fI320wg1eBWjNWivJucA7-lyrg__","token":"aL0BIJp~PBpgsMm4mte4aG-mdtwVjkxOV4STqA0lxy4_'
    };
    controller = new ViewTermsAndConditionsSigningUrlWithProgramController(options, {
      signerUrl: jest.fn().mockImplementation(() => signingUrl)
    } as unknown as TermsAndConditionsHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(signingUrl);
  });
});
