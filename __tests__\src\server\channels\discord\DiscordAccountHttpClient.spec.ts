import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import DiscordAccountHttpClient from "@src/server/channels/discord/DiscordAccountHttpClient";
import Random from "__tests__/factories/Random";

describe("DiscordAccountHttpClient", () => {
  it("removes a Discord account", async () => {
    const creatorId = Random.uuid();
    const id = Random.uuid();
    const client = { delete: jest.fn().mockReturnValue(Promise.resolve()) };
    const discordClient = new DiscordAccountHttpClient(client as unknown as TraceableHttpClient);

    discordClient.disconnectDiscordAccount(creatorId, id);

    expect(client.delete).toHaveBeenCalledTimes(1);
    expect(client.delete).toHaveBeenCalledWith(`/v1/creators/${creatorId}/discord-accounts/${id}`);
  });
});
