import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import TermsAndConditionsHttpClient from "./TermsAndConditionsHttpClient";

@Service()
class ViewTermsAndConditionsSigningUrlWithProgramController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly termsAndConditions: TermsAndConditionsHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const signingUrlResponse = await this.termsAndConditions.signerUrl({ ...req.body });
    this.json(res, signingUrlResponse);
  }
}

export default ViewTermsAndConditionsSigningUrlWithProgramController;
