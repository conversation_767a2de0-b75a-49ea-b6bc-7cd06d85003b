import "reflect-metadata";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import LegalDocumentsHttpClient from "@src/server/legalDocuments/LegalDocumentsHttpClient";
import { someSignedLegalDocuments } from "__tests__/factories/legalDocuments/SignedLegalDocuments";

describe("LegalDocumentsHttpClient", () => {
  it("gets signed contract legal documents", async () => {
    const id = "a0YK0000004zHaoMAE";
    const signedLegalDocuments = someSignedLegalDocuments();
    const client = {
      get: jest.fn().mockImplementation((url) => {
        if (url === `/v1/accepted-terms-and-conditions/${id}`) {
          return { data: { history: signedLegalDocuments.history } };
        } else {
          return { data: signedLegalDocuments.contracts };
        }
      })
    };
    const legalDocuments = new LegalDocumentsHttpClient(client as unknown as TraceableHttpClient);

    const legalDocumentsResponse = await legalDocuments.allWithSignature(id);

    expect(client.get).toHaveBeenCalledTimes(2);
    expect(client.get).toHaveBeenCalledWith(`/v1/accepted-terms-and-conditions/${id}`);
    expect(client.get).toHaveBeenCalledWith(`/v2/signed-contracts`, { query: { creatorId: id } });
    expect(signedLegalDocuments).toEqual(legalDocumentsResponse);
  });
});
