import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import ConnectedDiscordAccount from "@src/server/channels/discord/ConnectedDiscordAccount";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import onError from "@src/middleware/OnError";
import session from "@src/middleware/Session";
import withCors from "@src/middleware/WithCors";
import corsPreflight from "@src/middleware/CorsPreflight";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .delete(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const params = req.query;
      const user = req.session.identity;
      const discordAccount: ConnectedDiscordAccount = ApiContainer.get("discordAccount");
      await discordAccount.disconnectDiscordAccount(user.id, params.id as string);

      res.status(200);
      res.end();
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
