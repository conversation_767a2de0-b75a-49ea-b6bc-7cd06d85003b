import { Factory } from "fishery";
import { aSignedTermsAndConditions } from "./SignedTermsAndConditions";
import { aSignedContract } from "./SignedContract";
import SignedLegalDocuments from "@src/server/legalDocuments/SignedLegalDocuments";

const factory = Factory.define<SignedLegalDocuments>(() => ({
  contracts: [aSignedContract()],
  history: [aSignedTermsAndConditions()]
}));

export function someSignedLegalDocuments(override = {}): SignedLegalDocuments {
  return factory.build(override);
}
