---
currentMenu: howto
---

# How to

- [Add new pages](how-to/add-new-pages.md)
- [Module Federation](how-to/module-federation.md)
- [Implement page components](how-to/implement-page-components.md)
- [Structure form components](how-to/structure-form-components.md)
- [Write effective tests](how-to/testing.md)
- [Scaffold an API route](how-to/scaffold-api.md)
- [Import CSS from Node modules](#import-css-from-node-modules)
- [Generate SVG icon components](#generate-svg-icon-components)

## Import CSS from Node modules

CSS coming from a third-party packages cannot be imported using a CSS `@import`.
CSS from modules must be imported from `_app.js` with a ES6 `import` as shown in the snippet below.

```tsx
import "@eait-playerexp-cn/core-ui-kit/dist/style/core-ui-kit.css";
```

## Generate SVG icon components

```bash
npx @svgr/cli --template components/templates/Svgr.js public/icons --ignore-existing --out-dir components/icons --icon --replace-attr-values "#767676=currentColor"
```
