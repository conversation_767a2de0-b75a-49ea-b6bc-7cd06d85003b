import "reflect-metadata";
import type { NextApiRequest, NextApiResponse } from "next";
import { createRouter } from "next-connect";
import ApiContainer from "@src/ApiContainer";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import corsPreflight from "@src/middleware/CorsPreflight";
import onError from "@src/middleware/OnError";
import withCors from "@src/middleware/WithCors";
import session from "@src/middleware/Session";
import ViewTermsAndConditionsSigningUrlWithProgramController from "@src/server/pactSafe/ViewTermsAndConditionsSigningUrlWithProgramController";

const router = createRouter<NextApiRequest, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .post(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(ViewTermsAndConditionsSigningUrlWithProgramController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
