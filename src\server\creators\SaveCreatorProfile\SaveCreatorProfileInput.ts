import CommunicationPreferencesInput, { CommunicationPreferencesFormValues } from "./CommunicationPreferencesInput";
import PreferredFranchisesInput, { PreferredFranchisesFormValues } from "./PreferredFranchisesInput";
import { PreferredPlatformsFormValues } from "./PreferredPlatformsInput";
import CreatorsTypeInput, { CreatorsTypeFormValues } from "./CreatorsTypeInput";
import AdditionalInformationInput, { AdditionalInformationFormValues } from "./AdditionalInformationInput";
import LegalEntityInformationInput, { LegalEntityInformationFormValues } from "./LegalEntityInformationInput";
import AccountInformationInput, { AccountInformationFormValues } from "../AccountInformationInput";
import { User } from "@eait-playerexp-cn/server-kernel";
import MailingAddressInput, { MailingAddressFormValues } from "../MailingAddressInput";

export default class SaveCreatorProfileInput {
  public id?: string;
  readonly creatorTypes?: Array<string> = null;
  readonly accountInformation?: AccountInformationInput = null;
  readonly mailingAddress?: MailingAddressInput = null;
  readonly communicationPreferences?: CommunicationPreferencesInput = null;
  readonly additionalInformation?: AdditionalInformationInput = null;
  readonly preferredPlatforms: Array<{ id: string; type: string }> = [];
  readonly preferredFranchises: Array<{ id: string; type: string }> = [];
  readonly legalInformation?: LegalEntityInformationInput;
  readonly creatorSocialLinks: Array<string> = [];
  readonly creatorConnectedProgram: string = "";

  constructor(
    creator: User,
    values: {
      readonly accountInformation: AccountInformationFormValues;
      readonly mailingAddress: MailingAddressFormValues;
      readonly legalInformation: LegalEntityInformationFormValues;
      readonly additionalInformation: AdditionalInformationFormValues;
      readonly information: AccountInformationFormValues &
        MailingAddressFormValues &
        PreferredPlatformsFormValues & { creatorConnectedProgram: string };
      readonly franchisesYouPlay: PreferredFranchisesFormValues;
      readonly platformPreferences: PreferredPlatformsFormValues;
      readonly creatorTypes: CreatorsTypeFormValues;
      readonly communicationPreferences: CommunicationPreferencesFormValues;
      readonly creatorSocialLinks: Array<string>;
      readonly creatorConnectedProgram: string;
    },
    readonly registerCode: string = null
  ) {
    this.id = creator.id || null;

    if (values.creatorConnectedProgram != undefined) {
      this.creatorConnectedProgram = values.creatorConnectedProgram;
    }

    if (values.creatorSocialLinks != undefined) {
      this.creatorSocialLinks = values.creatorSocialLinks;
    }

    if (values.accountInformation !== undefined) {
      this.accountInformation = new AccountInformationInput(creator, values.accountInformation);
    }
    if (values.mailingAddress !== undefined) {
      this.mailingAddress = new MailingAddressInput(values.mailingAddress);
    }
    if (values.additionalInformation !== undefined) {
      this.additionalInformation = new AdditionalInformationInput(values.additionalInformation);
    }
    if (values.legalInformation !== undefined) {
      this.legalInformation = new LegalEntityInformationInput(values.legalInformation);
    }
    if (values.information !== undefined) {
      this.accountInformation = new AccountInformationInput(creator, values.information);
      this.mailingAddress = new MailingAddressInput(values.information);
      this.preferredPlatforms = [];
      this.creatorConnectedProgram = values.information.creatorConnectedProgram;
    }
    this.preferredPlatforms = [];

    if (values.creatorTypes !== undefined) {
      this.creatorTypes = new CreatorsTypeInput(values.creatorTypes).values;
    }

    if (values.franchisesYouPlay !== undefined) {
      this.preferredFranchises = new PreferredFranchisesInput(values.franchisesYouPlay).values;
    }

    if (values.communicationPreferences !== undefined) {
      this.communicationPreferences = new CommunicationPreferencesInput(values.communicationPreferences);
    }
  }

  isNew(): boolean {
    return this.id === null;
  }

  setId(id: string): void {
    this.id = id;
  }
}
