import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import config from "config";
import ContentScanningHttpClient, { UnreviewedCreatorCode } from "./ContentScanningHttpClient";

@Service()
class VerifyCreatorCodeController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly contentScanning: ContentScanningHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const nucleusId = this.identity(req).nucleusId;
    const programCode = config.PROGRAM_CODE;
    const deviceType = config.DEVICE_TYPE;
    const platform = config.PLATFORM;
    const additionalValues = {
      nucleusId,
      programCode,
      deviceType,
      platform
    };
    const creatorCodeRequestBody: UnreviewedCreatorCode = { ...req.body, ...additionalValues };

    const { data } = await this.contentScanning.verifyCreatorCode(creatorCodeRequestBody);

    this.json(res, data);
  }
}

export default VerifyCreatorCodeController;
