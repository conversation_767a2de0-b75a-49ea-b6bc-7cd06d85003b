import { Inject, Service } from "typedi";
import { NextApiRequest, NextApiResponse } from "next";
import { Controller, RequestHandler, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import ContentScanningHttpClient, { ContentUrls, ScanType } from "../creatorCode/ContentScanningHttpClient";

@Service()
class VerifyContentUrlController extends RequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private contentScanning: ContentScanningHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequest, res: NextApiResponse): Promise<void> {
    const { urls }: { urls: ContentUrls } = req.body;
    const type = req.query?.type as ScanType;
    const { data } = await this.contentScanning.verifyUrls(urls, type);
    this.json(res, data);
  }
}

export default VerifyContentUrlController;
