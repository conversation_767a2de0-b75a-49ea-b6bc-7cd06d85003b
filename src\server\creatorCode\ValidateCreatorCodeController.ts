import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import { Controller, NextApiRequestWithSession, type RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import ValidatedCreatorCodesHttpClient from "./ValidatedCreatorCodesHttpClient";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
class ValidateCreatorCodeController extends AuthenticatedRequestHandler implements Controller {
  constructor(
    @Inject("options") options: RequestHandlerOptions,
    private readonly validatedCreatorCodesHttpClient: ValidatedCreatorCodesHttpClient
  ) {
    super(options);
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const creatorCode = req.query.code as string;
    const creatorId = this.identity(req).id;

    const creator = await this.validatedCreatorCodesHttpClient.validateCreatorCode(creatorCode, creatorId);

    this.json(res, creator);
  }
}

export default ValidateCreatorCodeController;
