import { Factory } from "fishery";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { Random } from "@eait-playerexp-cn/onboarding-ui";
import { SignedTermsAndConditions } from "@src/server/legalDocuments/SignedLegalDocuments";

const factory = Factory.define<SignedTermsAndConditions>(() => ({
  creatorId: Random.uuid(),
  termsAndConditionsId: Random.uuid(),
  acceptedOnDate: LocalizedDate.epochMinusMonths(12),
  documentUrl: Random.url(),
  content: Random.string()
}));

export function aSignedTermsAndConditions(override = {}): SignedTermsAndConditions {
  return factory.build(override);
}
