import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import onError from "@src/middleware/OAuthErrorHandler";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import ApiContainer from "@src/ApiContainer";
import ConnectFacebookPageController from "@src/server/channels/facebook/ConnectFacebookPageController";
import withCors from "@src/middleware/WithCors";
import corsPreflight from "@src/middleware/CorsPreflight";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .post(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(ConnectFacebookPageController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
