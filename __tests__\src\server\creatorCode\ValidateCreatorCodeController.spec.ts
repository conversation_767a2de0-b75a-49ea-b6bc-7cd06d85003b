import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import ValidateCreatorCodeController from "@src/server/creatorCode/ValidateCreatorCodeController";
import ValidatedCreatorCodesHttpClient from "@src/server/creatorCode/ValidatedCreatorCodesHttpClient";
import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";

describe("ValidateCreatorCodeController", () => {
  let controller: ValidateCreatorCodeController;
  const options = {} as RequestHandlerOptions;
  const identity = Identity.fromStored(aStoredIdentity());

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("validates a creator code and returns the result", async () => {
    const creatorCode = "TEST123";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/creator-codes/${creatorCode}`,
      query: { code: creatorCode }
    });
    req.session = { identity, save: jest.fn() };
    const validationResult = {
      creatorCode: "TEST123",
      creatorId: identity.id,
      isValidCode: true
    };
    const client = { validateCreatorCode: jest.fn().mockResolvedValue(validationResult) };
    controller = new ValidateCreatorCodeController(options, client as unknown as ValidatedCreatorCodesHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(validationResult);
    expect(client.validateCreatorCode).toHaveBeenCalledTimes(1);
    expect(client.validateCreatorCode).toHaveBeenCalledWith(creatorCode, identity.id);
  });
});
