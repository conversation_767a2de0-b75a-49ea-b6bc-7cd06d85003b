import { Inject, Service } from "typedi";
import TermsAndConditionsHttpClient from "./TermsAndConditionsHttpClient";
import SignedStatus from "./SignedStatus";
import SignerInformation from "./SignerInformation";
import { RedisCache } from "@eait-playerexp-cn/server-kernel";
import config from "../../../config";

export type SignerUrlWithTierOrProgram = {
  contractUrl: string;
};

@Service()
export default class CachedTermsAndConditions {
  private static readonly CACHE_KEY = "signedStatus";

  constructor(
    @Inject("termsAndConditions")
    private readonly termsAndConditions: TermsAndConditionsHttpClient,
    private readonly cache: RedisCache
  ) {}

  async signedStatusWithProgram(creatorId: string, locale: string, programCode: string): Promise<SignedStatus> {
    const cacheKey: string = this.getCacheKey(creatorId, locale, programCode);
    if (await this.cache.has(cacheKey)) {
      return (await this.cache.get(cacheKey)) as SignedStatus;
    }
    const status = await this.termsAndConditions.signedStatusWithProgram(creatorId);
    await this.cache.set(cacheKey, status, config.TERMS_STATUS_CACHE_TTL);

    return Promise.resolve(status);
  }

  signerUrl(signer: SignerInformation): Promise<Record<string, unknown>> {
    return this.termsAndConditions.signerUrl(signer);
  }

  clearSignedStatusForProgram(creatorId: string, locale: string, programCode: string): Promise<void> {
    const cacheKey: string = this.getCacheKey(creatorId, locale, programCode);

    return this.cache.delete(cacheKey);
  }

  private getCacheKey(creatorId: string, locale: string, programCode: string): string {
    return `${CachedTermsAndConditions.CACHE_KEY}_${creatorId}_${locale}_${programCode}`;
  }
}
