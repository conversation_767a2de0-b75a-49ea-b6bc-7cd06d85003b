import { NextApiResponse } from "next";
import { Inject, Service } from "typedi";
import {
  Controller,
  NextApiRequestWithSession,
  RequestHandler,
  type RequestHandlerOptions
} from "@eait-playerexp-cn/server-kernel";

@Service()
class HealthCheckController extends RequestHandler implements Controller {
  constructor(@Inject("options") options: RequestHandlerOptions) {
    super(options);
  }

  async handle(_req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    return this.json(res, { status: "UP" });
  }
}

export default HealthCheckController;
