type PreferredPlatformFormValues = { value: string; label: string };

class PreferredPlatform {
  readonly id: string;
  readonly name: string;

  constructor(
    values: PreferredPlatformFormValues,
    readonly type: string
  ) {
    this.id = values.value;
    this.name = values.label;
  }
}

export type PreferredPlatformsFormValues = {
  primaryPlatform: PreferredPlatformFormValues;
  secondaryPlatforms: PreferredPlatformFormValues[];
};

export default class PreferredPlatformsInput {
  readonly values: PreferredPlatform[];

  constructor(values: PreferredPlatformsFormValues) {
    this.values = values.secondaryPlatforms.map((values) => new PreferredPlatform(values, "SECONDARY"));
    this.values.push(new PreferredPlatform(values.primaryPlatform, "PRIMARY"));
  }
}
