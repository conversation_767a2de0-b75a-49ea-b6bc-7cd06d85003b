{"compilerOptions": {"baseUrl": ".", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "emitDecoratorMetadata": true, "experimentalDecorators": true, "incremental": true, "paths": {"@components/*": ["src/components/*"], "@src/*": ["src/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "globals.d.ts", "**/*.ts", "**/*.tsx", "jest.setup.ts"], "exclude": ["node_modules", "__mocks__", "analytics"]}