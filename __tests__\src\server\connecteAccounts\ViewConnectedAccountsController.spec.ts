import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import ViewConnectedAccountsController from "@src/server/connecteAccounts/ViewConnectedAccountsController";
import config from "../../../../config";
import ConnectedAccountsHttpClient from "@src/server/connecteAccounts/ConnectedAccountsHttpClient";
import { Random } from "@eait-playerexp-cn/onboarding-ui";

jest.mock("../../../../config");

describe("ViewConnectedAccountsController", () => {
  let controller: ViewConnectedAccountsController;
  const options = {} as RequestHandlerOptions;
  const session = { save: jest.fn() };
  const account = {
    name: Random.firstName(),
    disconnected: false,
    username: Random.userName(),
    id: Random.uuid(),
    type: "YOUTUBE",
    uri: Random.url(),
    thumbnail: Random.imageUrl(),
    isExpired: true,
    accountId: Random.uuid()
  };

  beforeEach(() => jest.clearAllMocks());

  it("fetch connected accounts for the interested creators", async () => {
    config.INTERESTED_CREATOR_REAPPLY_PERIOD = false;
    const nucleusId = "abc124444cfhfkflddfgfh";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: "/api/connected-accounts",
      query: { nucleusId },
      session
    });
    const connectedAccounts = [account];
    const accounts = { getAllConnectedAccounts: jest.fn().mockResolvedValue(connectedAccounts) };
    controller = new ViewConnectedAccountsController(options, accounts as unknown as ConnectedAccountsHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(connectedAccounts);
    expect(accounts.getAllConnectedAccounts).toHaveBeenCalledTimes(1);
    expect(accounts.getAllConnectedAccounts).toHaveBeenCalledWith(nucleusId);
    expect(req.session.nucleusId).toEqual(nucleusId);
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });

  it("finds all connected accounts for an interested creator with expiration status", async () => {
    config.INTERESTED_CREATOR_REAPPLY_PERIOD = true;
    const nucleusId = "abc124444cfhfkflddfgfh";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: "/api/v2/connected-accounts",
      query: { nucleusId },
      session
    });
    const connectedAccounts = [account];
    const accounts = { getAllConnectedAccountsWithExpirationStatus: jest.fn().mockResolvedValue(connectedAccounts) };
    controller = new ViewConnectedAccountsController(options, accounts as unknown as ConnectedAccountsHttpClient);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(connectedAccounts);
    expect(accounts.getAllConnectedAccountsWithExpirationStatus).toHaveBeenCalledTimes(1);
    expect(accounts.getAllConnectedAccountsWithExpirationStatus).toHaveBeenCalledWith(nucleusId);
    expect(req.session.nucleusId).toEqual(nucleusId);
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });
});
