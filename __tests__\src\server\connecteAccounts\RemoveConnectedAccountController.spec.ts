import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession, RequestHandlerOptions } from "@eait-playerexp-cn/server-kernel";
import RemoveConnectedAccountController from "@src/server/connecteAccounts/RemoveConnectedAccountController";
import ConnectedAccountsHttpClient from "@src/server/connecteAccounts/ConnectedAccountsHttpClient";

describe("RemoveConnectedAccountController", () => {
  let controller: RemoveConnectedAccountController;
  const options = {} as RequestHandlerOptions;
  const session = { save: jest.fn() };

  beforeEach(() => jest.clearAllMocks());

  it("removes interested creator account", async () => {
    const nucleusId = "************";
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "DELETE",
      url: "api/remove-account",
      query: { id: "a1LDF00000K4z0i2AB" },
      session: { ...session, nucleusId }
    });
    const accounts = { removeConnectedAccount: jest.fn() } as unknown as ConnectedAccountsHttpClient;
    controller = new RemoveConnectedAccountController(options, accounts);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(accounts.removeConnectedAccount).toHaveBeenCalledTimes(1);
    expect(accounts.removeConnectedAccount).toHaveBeenCalledWith(req.query.id, "INTERESTED_CREATOR");
  });

  it("removes creator account", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "DELETE",
      url: "api/remove-account",
      query: { id: "a1LDF00000K4z0i2AB" },
      session: { ...session, user: {} }
    });
    const accounts = { removeConnectedAccount: jest.fn() } as unknown as ConnectedAccountsHttpClient;
    controller = new RemoveConnectedAccountController(options, accounts);

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(accounts.removeConnectedAccount).toHaveBeenCalledTimes(1);
    expect(accounts.removeConnectedAccount).toHaveBeenCalledWith(req.query.id, "CREATOR");
  });
});
