import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import Api<PERSON>ontainer from "@src/ApiContainer";
import ViewConnectedAccountsController from "@src/server/connecteAccounts/ViewConnectedAccountsController";
import onError from "@src/middleware/OnError";
import withCors from "@src/middleware/WithCors";
import corsPreflight from "@src/middleware/CorsPreflight";
import session from "@src/middleware/Session";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(verifySession)
  .use(addTelemetryInformation)
  .get(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(ViewConnectedAccountsController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
