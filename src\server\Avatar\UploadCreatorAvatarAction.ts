import { Inject, Service } from "typedi";
import AvatarInput from "./AvatarInput";
import CreatorsHttpClient from "@src/server/creators/CreatorsHttpClient";

@Service()
class UploadCreatorAvatarAction {
  constructor(
    @Inject("creators")
    private readonly creators: CreatorsHttpClient
  ) {}

  async execute(input: AvatarInput): Promise<void> {
    await this.creators.upload(input.uploadedImage());
  }
}

export default UploadCreatorAvatarAction;
